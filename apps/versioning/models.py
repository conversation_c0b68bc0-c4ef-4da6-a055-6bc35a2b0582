"""Universal Version Control System Models

Provides git-like versioning capabilities for any Django model with:
- Automatic versioning with compression
- Branch and merge workflows
- Retention policies and optimization
- Comprehensive audit trails
- Performance metrics tracking
"""

import base64
from collections import defaultdict
import gzip
import json
import uuid
from datetime import timed<PERSON><PERSON>
from typing import Any, ClassVar, Optional

from django.contrib.contenttypes.fields import GenericForeignKey
from django.contrib.contenttypes.models import ContentType
from django.contrib.gis.db import models
from django.core.serializers.json import DjangoJSONEncoder
from django.db import transaction
from django.db.models import Avg, Count, Q, Sum
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

from apps.authentication.models import User


class CompressedDataField(models.JSONField):
    """Custom JSON field that automatically compresses large data.

    Compresses JSON data above threshold using gzip and base64 encoding.
    Transparently decompresses when reading from database.
    """

    def __init__(self, *args, compression_threshold: int = 1024, **kwargs):
        """Initialize compressed data field.

        Args:
        ----
            compression_threshold: Size in bytes above which to compress data

        """
        self.compression_threshold = compression_threshold
        super().__init__(*args, **kwargs)

    def get_prep_value(self, value: Any) -> dict | Any:
        """Prepare value for database storage with optional compression"""
        if value is None:
            return None

        # Convert to JSON string first
        json_str = json.dumps(value, cls=DjangoJSONEncoder, separators=(",", ":"))
        json_bytes = json_str.encode("utf-8")

        # Compress if above threshold
        if len(json_bytes) > self.compression_threshold:
            compressed = gzip.compress(json_bytes)
            return {
                "__compressed__": True,
                "data": base64.b64encode(compressed).decode("utf-8"),
                "original_size": len(json_bytes),
                "compressed_size": len(compressed),
            }
        return json.loads(json_str)  # Return as normal JSON

    def from_db_value(self, value: Any, expression, connection) -> Any:
        """Convert value from database, decompressing if needed"""
        if value is None:
            return None

        # Check if data is compressed
        if isinstance(value, dict) and value.get("__compressed__"):
            try:
                compressed_data = base64.b64decode(value["data"])
                json_str = gzip.decompress(compressed_data).decode("utf-8")
                return json.loads(json_str)
            except (json.JSONDecodeError, ValueError):
                # Fallback to original value if decompression fails
                return value

        return value

    def to_python(self, value: Any) -> Any:
        """Convert value to Python object, decompressing if needed"""
        if value is None:
            return None

        # Check if data is compressed
        if isinstance(value, dict) and value.get("__compressed__"):
            try:
                compressed_data = base64.b64decode(value["data"])
                json_str = gzip.decompress(compressed_data).decode("utf-8")
                return json.loads(json_str)
            except (json.JSONDecodeError, ValueError):
                return value

        return value


class BaseVersionManager(models.Manager):
    """Manager for versioned models with optimization methods"""

    def get_latest_version(self, obj) -> Optional["BaseVersion"]:
        """Get the latest version for an object"""
        content_type = ContentType.objects.get_for_model(obj)
        return self.filter(content_type=content_type, object_id=obj.pk).order_by("-version_number").first()

    def get_versions_for_object(self, obj, limit: int | None = None):
        """Get all versions for an object"""
        content_type = ContentType.objects.get_for_model(obj)
        queryset = self.filter(content_type=content_type, object_id=obj.pk).order_by("-version_number")

        if limit:
            queryset = queryset[:limit]

        return queryset

    def get_significant_versions(self, obj):
        """Get only significant versions for an object"""
        content_type = ContentType.objects.get_for_model(obj)
        return self.filter(content_type=content_type, object_id=obj.pk, is_significant=True).order_by("-version_number")


class BaseVersion(models.Model):
    """Abstract base model for versioning any Django model.

    Provides core versioning functionality with compression, branching,
    and comprehensive tracking of changes over time.
    """

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Generic relation to any model
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE, verbose_name=_("content type"))
    object_id = models.UUIDField(verbose_name=_("object ID"))
    content_object = GenericForeignKey("content_type", "object_id")

    # Version tracking
    version_number = models.PositiveIntegerField(
        verbose_name=_("version number"),
        help_text=_("Sequential version number for this object"),
    )
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_("created at"))
    created_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name="created_%(class)s_versions",
        verbose_name=_("created by"),
    )

    # Change tracking
    change_summary = models.TextField(
        blank=True,
        null=True,
        verbose_name=_("change summary"),
        help_text=_("Description of what changed in this version"),
    )
    serialized_data = CompressedDataField(
        encoder=DjangoJSONEncoder,
        verbose_name=_("serialized data"),
        help_text=_("Complete model state snapshot (compressed if large)"),
        compression_threshold=2048,  # Compress if over 2KB
    )

    # Storage optimization fields
    is_significant = models.BooleanField(
        default=True,
        verbose_name=_("is significant"),
        help_text=_("Mark version as significant (kept during pruning)"),
    )
    data_size = models.PositiveIntegerField(
        default=0,
        verbose_name=_("data size"),
        help_text=_("Original size of serialized data in bytes"),
    )
    compression_ratio = models.FloatField(
        default=1.0,
        verbose_name=_("compression ratio"),
        help_text=_("Compression ratio (compressed_size / original_size)"),
    )

    # Branching support
    parent_version = models.ForeignKey(
        "self",
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name="child_versions",
        verbose_name=_("parent version"),
    )
    branch_name = models.CharField(max_length=100, blank=True, null=True, verbose_name=_("branch name"))
    is_merged = models.BooleanField(default=False, verbose_name=_("is merged"))
    merged_at = models.DateTimeField(blank=True, null=True, verbose_name=_("merged at"))
    merged_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name="merged_%(class)s_versions",
        verbose_name=_("merged by"),
    )

    # Metadata
    metadata = models.JSONField(
        default=dict,
        blank=True,
        verbose_name=_("metadata"),
        help_text=_("Additional version metadata"),
    )

    objects = BaseVersionManager()

    class Meta:
        abstract = True
        verbose_name = _("version")
        verbose_name_plural = _("versions")
        constraints: ClassVar = [
            models.UniqueConstraint(
                fields=["content_type", "object_id", "version_number"],
                name="%(class)s_unique_version",
            ),
        ]
        indexes: ClassVar = [
            models.Index(
                fields=["content_type", "object_id", "-version_number"],
                name="%(class)s_content_version_idx",
            ),
            models.Index(fields=["created_by", "-created_at"], name="%(class)s_creator_idx"),
            models.Index(fields=["branch_name"], name="%(class)s_branch_idx"),
            models.Index(fields=["is_significant"], name="%(class)s_significant_idx"),
            models.Index(fields=["data_size"], name="%(class)s_size_idx"),
            models.Index(
                fields=["content_type", "object_id", "is_significant", "-created_at"],
                name="%(class)s_pruning_idx",
            ),
        ]
        ordering: ClassVar = ["-version_number"]

    def __str__(self) -> str:
        return f"{self.content_type.model} v{self.version_number}"

    def save(self, *args, **kwargs):
        """Override save to calculate data size and compression ratio"""
        if self.serialized_data:
            # Calculate original size
            json_str = json.dumps(self.serialized_data, cls=DjangoJSONEncoder, separators=(",", ":"))
            self.data_size = len(json_str.encode("utf-8"))

            # Calculate compression ratio if data is compressed
            if isinstance(self.serialized_data, dict) and self.serialized_data.get("__compressed__"):
                compressed_size = self.serialized_data.get("compressed_size", 0)
                original_size = self.serialized_data.get("original_size", 1)
                self.compression_ratio = compressed_size / original_size if original_size > 0 else 1.0
            else:
                self.compression_ratio = 1.0

        super().save(*args, **kwargs)

    def get_storage_info(self) -> dict[str, Any]:
        """Get information about storage optimization"""
        is_compressed = isinstance(self.serialized_data, dict) and self.serialized_data.get("__compressed__", False)

        compressed_size = int(self.data_size * self.compression_ratio) if is_compressed else self.data_size
        space_saved = self.data_size - compressed_size if is_compressed else 0

        return {
            "is_compressed": is_compressed,
            "original_size": self.data_size,
            "compressed_size": compressed_size,
            "compression_ratio": self.compression_ratio,
            "space_saved": space_saved,
            "is_significant": self.is_significant,
        }

    @property
    def log_name(self) -> str:
        """Generate log namespace for this version"""
        return f"{self.content_type.app_label}.{self.content_type.model}s.log"

    def get_model_changes(self, previous_version: Optional["BaseVersion"] = None) -> dict[str, Any]:
        """Compare this version with previous version to get changes.

        Args:
        ----
            previous_version: Previous version to compare against

        Returns:
        -------
            Dictionary containing change information

        """
        if not previous_version:
            # First version - all fields are "new"
            return {
                "action": "created",
                "changed_fields": (list(self.serialized_data.keys()) if self.serialized_data else []),
                "field_changes": {k: {"new": v, "old": None} for k, v in (self.serialized_data or {}).items()},
            }

        changes = {}
        changed_fields = []

        old_data = previous_version.serialized_data or {}
        new_data = self.serialized_data or {}

        # Check for changed fields
        for field, new_value in new_data.items():
            old_value = old_data.get(field)
            if old_value != new_value:
                changes[field] = {"old": old_value, "new": new_value}
                changed_fields.append(field)

        # Check for removed fields
        for field, old_value in old_data.items():
            if field not in new_data:
                changes[field] = {"old": old_value, "new": None}
                changed_fields.append(field)

        return {
            "action": "updated",
            "changed_fields": changed_fields,
            "field_changes": changes,
        }

    def create_branch(self, branch_name: str, user: User, description: str = "") -> "VersionBranch":
        """Create a new branch from this version"""
        from .models import VersionBranch

        content_type = ContentType.objects.get_for_model(self.content_object)

        return VersionBranch.objects.create(
            content_type=content_type,
            object_id=self.object_id,
            branch_name=branch_name,
            description=description,
            branched_from_version=self,
            created_by=user,
        )


class UniversalVersion(BaseVersion):
    """Concrete implementation of BaseVersion for any model.
    This is the actual table that stores version data.
    """

    class Meta:
        app_label = "versioning"
        db_table: ClassVar = "versioning_universal_version"
        verbose_name = _("universal version")
        verbose_name_plural = _("universal versions")
        constraints: ClassVar = [
            models.UniqueConstraint(
                fields=["content_type", "object_id", "version_number"],
                name="universal_version_unique",
            ),
        ]
        indexes: ClassVar = [
            models.Index(
                fields=["content_type", "object_id", "-version_number"],
                name="universal_version_content_idx",
            ),
            models.Index(
                fields=["created_by", "-created_at"],
                name="universal_version_creator_idx",
            ),
            models.Index(fields=["branch_name"], name="universal_version_branch_idx"),
            models.Index(fields=["is_significant"], name="uv_significant_idx"),
            models.Index(fields=["data_size"], name="universal_version_size_idx"),
            models.Index(
                fields=["content_type", "object_id", "is_significant", "-created_at"],
                name="universal_version_pruning_idx",
            ),
        ]
        ordering: ClassVar = ["-version_number"]


class VersionPruningPolicy(models.Model):
    """Configuration for version pruning policies.

    Defines rules for automatically cleaning up old versions
    to manage storage and performance.
    """

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Policy identification
    name = models.CharField(max_length=100, unique=True, verbose_name=_("name"))
    description = models.TextField(blank=True, verbose_name=_("description"))

    # Model targeting
    content_type = models.ForeignKey(
        ContentType,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name=_("content type"),
        help_text=_("Specific model to apply policy to (null = all models)"),
    )

    # Pruning rules
    max_versions = models.PositiveIntegerField(
        default=50,
        verbose_name=_("max versions"),
        help_text=_("Maximum number of versions to keep"),
    )
    max_age_days = models.PositiveIntegerField(
        default=365,
        verbose_name=_("max age days"),
        help_text=_("Maximum age in days for versions"),
    )
    keep_significant_only = models.BooleanField(
        default=False,
        verbose_name=_("keep significant only"),
        help_text=_("Only keep versions marked as significant"),
    )
    min_versions_to_keep = models.PositiveIntegerField(
        default=5,
        verbose_name=_("min versions to keep"),
        help_text=_("Minimum number of versions to always keep"),
    )

    # Frequency and automation
    auto_prune = models.BooleanField(
        default=False,
        verbose_name=_("auto prune"),
        help_text=_("Automatically prune versions"),
    )
    prune_frequency_days = models.PositiveIntegerField(
        default=7,
        verbose_name=_("prune frequency days"),
        help_text=_("How often to run auto-pruning (days)"),
    )

    # Status tracking
    is_active = models.BooleanField(default=True, verbose_name=_("is active"))

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_("created at"))
    updated_at = models.DateTimeField(auto_now=True, verbose_name=_("updated at"))
    last_applied = models.DateTimeField(null=True, blank=True, verbose_name=_("last applied"))

    class Meta:
        app_label = "versioning"
        db_table: ClassVar = "versioning_pruning_policy"
        verbose_name = _("version pruning policy")
        verbose_name_plural = _("version pruning policies")
        ordering = ["name"]

    def __str__(self) -> str:
        return f"Pruning Policy: {self.name}"

    def should_prune(self) -> bool:
        """Check if it's time to run this pruning policy"""
        if not self.auto_prune or not self.is_active:
            return False

        if not self.last_applied:
            return True

        days_since_last = (timezone.now() - self.last_applied).days
        return days_since_last >= self.prune_frequency_days

    def get_versions_to_prune(self, model_instance=None) -> models.QuerySet:
        """Get versions that should be pruned according to this policy.

        Args:
        ----
            model_instance: Specific instance to prune (optional)

        Returns:
        -------
            QuerySet of versions to prune

        """
        queryset = UniversalVersion.objects.all()

        # Filter by content type if specified
        if self.content_type:
            queryset = queryset.filter(content_type=self.content_type)

        # Filter by specific model instance if provided
        if model_instance:
            content_type = ContentType.objects.get_for_model(model_instance)
            queryset = queryset.filter(content_type=content_type, object_id=model_instance.pk)

        # Apply age filter
        if self.max_age_days:
            cutoff_date = timezone.now() - timedelta(days=self.max_age_days)
            queryset = queryset.filter(created_at__lt=cutoff_date)

        # Apply significance filter
        if self.keep_significant_only:
            queryset = queryset.filter(is_significant=False)

        return queryset.order_by("created_at")

    def apply_policy(self, dry_run: bool = False) -> dict[str, Any]:
        """Apply this pruning policy.

        Args:
        ----
            dry_run: If True, only return what would be pruned

        Returns:
        -------
            Dictionary with pruning results

        """
        versions_to_prune = self.get_versions_to_prune()

        # Group by object to respect min_versions_to_keep
        # from collections import defaultdict (moved to top level)

        by_object = defaultdict(list)

        for version in versions_to_prune:
            key = (version.content_type_id, version.object_id)
            by_object[key].append(version)

        pruned_count = 0
        pruning_results = []

        for (content_type_id, object_id), versions in by_object.items():
            # Keep minimum required versions
            total_versions = UniversalVersion.objects.filter(
                content_type_id=content_type_id,
                object_id=object_id,
            ).count()

            if total_versions <= self.min_versions_to_keep:
                continue

            # Apply max_versions limit
            versions_to_delete = versions
            if self.max_versions:
                max_to_delete = total_versions - self.max_versions
                if max_to_delete > 0:
                    versions_to_delete = versions[:max_to_delete]
                else:
                    continue

            for version in versions_to_delete:
                if not dry_run:
                    version.delete()
                pruned_count += 1
                pruning_results.append(
                    {
                        "version_id": version.id,
                        "version_number": version.version_number,
                        "content_type": str(version.content_type),
                        "object_id": str(version.object_id),
                    },
                )

        if not dry_run and pruned_count > 0:
            self.last_applied = timezone.now()
            self.save(update_fields=["last_applied"])

        return {
            "policy_name": self.name,
            "pruned_count": pruned_count,
            "dry_run": dry_run,
            "pruning_results": pruning_results,
        }


class VersionLog(models.Model):
    """Central logging system for all versioned model changes.
    Provides unified audit trail with namespace organization.
    """

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Log organization
    timestamp = models.DateTimeField(auto_now_add=True, verbose_name=_("timestamp"))
    log_name = models.CharField(
        max_length=100,
        db_index=True,
        verbose_name=_("log name"),
        help_text=_("Log namespace (e.g., 'persons.log', 'projects.log')"),
    )

    # Model reference
    model_name = models.CharField(max_length=100, verbose_name=_("model name"))
    object_id = models.UUIDField(verbose_name=_("object ID"))

    # Change tracking
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name="version_logs",
        verbose_name=_("user"),
    )
    action = models.CharField(
        max_length=50,
        verbose_name=_("action"),
        choices=[
            ("created", _("Created")),
            ("updated", _("Updated")),
            ("restored", _("Restored")),
            ("deleted", _("Deleted")),
            ("branched", _("Branched")),
            ("merged", _("Merged")),
        ],
    )
    summary = models.TextField(blank=True, null=True, verbose_name=_("summary"))

    # Version reference
    version = models.ForeignKey(
        UniversalVersion,
        on_delete=models.CASCADE,
        related_name="logs",
        null=True,
        blank=True,
        verbose_name=_("version"),
    )

    # Additional metadata
    metadata = models.JSONField(default=dict, blank=True, verbose_name=_("metadata"))

    # Performance tracking
    processing_time_ms = models.PositiveIntegerField(
        null=True,
        blank=True,
        verbose_name=_("processing time (ms)"),
        help_text=_("Time taken to process this operation in milliseconds"),
    )

    class Meta:
        app_label = "versioning"
        db_table: ClassVar = "versioning_log"
        verbose_name = _("version log")
        verbose_name_plural = _("version logs")
        indexes: ClassVar = [
            models.Index(fields=["log_name", "-timestamp"], name="version_log_namespace_idx"),
            models.Index(fields=["object_id", "-timestamp"], name="version_log_object_idx"),
            models.Index(fields=["user", "-timestamp"], name="version_log_user_idx"),
            models.Index(fields=["action", "-timestamp"], name="version_log_action_idx"),
            models.Index(fields=["processing_time_ms"], name="version_log_performance_idx"),
        ]
        ordering: ClassVar = ["-timestamp"]

    def __str__(self) -> str:
        username = self.user.username if self.user else "system"
        return f"{self.log_name}: {username} {self.action} {self.model_name}"

    def get_performance_info(self) -> dict[str, Any]:
        """Get performance information for this log entry"""
        return {
            "processing_time_ms": self.processing_time_ms,
            "action": self.action,
            "timestamp": self.timestamp,
            "user": self.user.username if self.user else "system",
            "model_name": self.model_name,
        }


class VersionBranch(models.Model):
    """Track version branches for complex workflows.
    Enables git-like branching and merging for version control.
    """

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Generic relation to any model
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE, verbose_name=_("content type"))
    object_id = models.UUIDField(verbose_name=_("object ID"))
    content_object = GenericForeignKey("content_type", "object_id")

    # Branch info
    branch_name = models.CharField(max_length=100, verbose_name=_("branch name"))
    description = models.TextField(blank=True, null=True, verbose_name=_("description"))

    # Branching history
    branched_from_version = models.ForeignKey(
        UniversalVersion,
        on_delete=models.CASCADE,
        related_name="branches",
        verbose_name=_("branched from version"),
    )
    created_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name="version_created_branches",
        verbose_name=_("created by"),
    )

    # Merge history
    is_merged = models.BooleanField(default=False, verbose_name=_("is merged"))
    merged_into_version = models.ForeignKey(
        UniversalVersion,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name="merged_branches",
        verbose_name=_("merged into version"),
    )
    merged_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name="branch_merges",
        verbose_name=_("merged by"),
    )
    merged_at = models.DateTimeField(blank=True, null=True, verbose_name=_("merged at"))

    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_("created at"))

    class Meta:
        app_label = "versioning"
        db_table: ClassVar = "versioning_branch"
        verbose_name = _("version branch")
        verbose_name_plural = _("version branches")
        constraints: ClassVar = [
            models.UniqueConstraint(
                fields=["content_type", "object_id", "branch_name"],
                name="version_branch_unique",
                condition=Q(is_merged=False),
            ),
        ]
        indexes: ClassVar = [
            models.Index(
                fields=["content_type", "object_id", "is_merged"],
                name="version_branch_content_idx",
            ),
        ]
        ordering = ["-created_at"]

    def __str__(self) -> str:
        return f"{self.content_type.model} - {self.branch_name}"

    @property
    def log_name(self) -> str:
        """Generate log namespace for this branch"""
        return f"{self.content_type.app_label}.{self.content_type.model}s.log"

    def merge(self, user: User, merge_summary: str = "") -> UniversalVersion:
        """Merge this branch back to main line.

        Args:
        ----
            user: User performing the merge
            merge_summary: Description of the merge

        Returns:
        -------
            The new merged version

        """
        if self.is_merged:
            raise ValueError("Branch is already merged")

        # Get the latest version on this branch
        latest_branch_version = (
            UniversalVersion.objects.filter(
                content_type=self.content_type,
                object_id=self.object_id,
                branch_name=self.branch_name,
            )
            .order_by("-version_number")
            .first()
        )

        if not latest_branch_version:
            raise ValueError("No versions found on this branch")

        # Get next version number for main line
        latest_main_version = (
            UniversalVersion.objects.filter(
                content_type=self.content_type,
                object_id=self.object_id,
                branch_name__isnull=True,
            )
            .order_by("-version_number")
            .first()
        )

        next_version = (latest_main_version.version_number + 1) if latest_main_version else 1

        # Create merged version
        with transaction.atomic():
            merged_version = UniversalVersion.objects.create(
                content_type=self.content_type,
                object_id=self.object_id,
                version_number=next_version,
                created_by=user,
                change_summary=merge_summary or f"Merged branch '{self.branch_name}'",
                serialized_data=latest_branch_version.serialized_data,
                is_significant=True,  # Merges are always significant
                parent_version=latest_branch_version,
                metadata={
                    "is_merge": True,
                    "merged_branch": self.branch_name,
                    "merged_from_version": str(latest_branch_version.id),
                },
            )

            # Update branch as merged
            self.is_merged = True
            self.merged_into_version = merged_version
            self.merged_by = user
            self.merged_at = timezone.now()
            self.save()

        return merged_version


class VersionDiff(models.Model):
    """Store computed differences between versions.
    Caches diff calculations for performance optimization.
    """

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    from_version = models.ForeignKey(
        UniversalVersion,
        on_delete=models.CASCADE,
        related_name="diffs_from",
        verbose_name=_("from version"),
    )
    to_version = models.ForeignKey(
        UniversalVersion,
        on_delete=models.CASCADE,
        related_name="diffs_to",
        verbose_name=_("to version"),
    )

    # Diff data
    diff_data = CompressedDataField(
        default=dict,
        verbose_name=_("diff data"),
        help_text=_("Structured diff information (compressed if large)"),
        compression_threshold=1024,
    )
    diff_summary = models.TextField(blank=True, null=True, verbose_name=_("diff summary"))
    changes_count = models.PositiveIntegerField(default=0, verbose_name=_("changes count"))

    # Performance metrics
    computation_time_ms = models.PositiveIntegerField(
        null=True,
        blank=True,
        verbose_name=_("computation time (ms)"),
        help_text=_("Time taken to compute this diff in milliseconds"),
    )

    # Metadata
    computed_at = models.DateTimeField(auto_now_add=True, verbose_name=_("computed at"))

    # Caching
    is_cached = models.BooleanField(default=False, verbose_name=_("is cached"))
    cache_hits = models.PositiveIntegerField(default=0, verbose_name=_("cache hits"))
    last_accessed = models.DateTimeField(auto_now=True, verbose_name=_("last accessed"))

    class Meta:
        app_label = "versioning"
        db_table: ClassVar = "versioning_diff"
        verbose_name = _("version diff")
        verbose_name_plural = _("version diffs")
        constraints: ClassVar = [
            models.UniqueConstraint(fields=["from_version", "to_version"], name="version_diff_unique"),
        ]
        indexes: ClassVar = [
            models.Index(fields=["from_version", "to_version"], name="version_diff_versions_idx"),
            models.Index(fields=["is_cached", "last_accessed"], name="version_diff_cache_idx"),
            models.Index(fields=["computation_time_ms"], name="version_diff_performance_idx"),
        ]

    def __str__(self) -> str:
        return f"Diff: v{self.from_version.version_number} → v{self.to_version.version_number}"

    def increment_cache_hit(self):
        """Increment cache hit counter"""
        self.cache_hits += 1
        self.save(update_fields=["cache_hits", "last_accessed"])


class VersionMetrics(models.Model):
    """Store metrics about version storage and performance.
    Provides insights for optimization and monitoring.
    """

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Model tracking
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE, verbose_name=_("content type"))
    object_id = models.UUIDField(verbose_name=_("object ID"))

    # Version metrics
    total_versions = models.PositiveIntegerField(default=0, verbose_name=_("total versions"))
    significant_versions = models.PositiveIntegerField(default=0, verbose_name=_("significant versions"))

    # Storage metrics
    total_storage_bytes = models.PositiveBigIntegerField(default=0, verbose_name=_("total storage bytes"))
    compressed_storage_bytes = models.PositiveBigIntegerField(default=0, verbose_name=_("compressed storage bytes"))
    avg_compression_ratio = models.FloatField(default=1.0, verbose_name=_("average compression ratio"))

    # Computed fields for easier querying
    total_storage_mb = models.GeneratedField(
        expression=models.F("total_storage_bytes") / 1024 / 1024,
        output_field=models.FloatField(),
        db_persist=True,
    )
    largest_version_mb = models.FloatField(default=0.0, verbose_name=_("largest version (MB)"))
    oldest_version_date = models.DateTimeField(null=True, blank=True, verbose_name=_("oldest version date"))

    # Performance tracking
    last_access_time = models.DateTimeField(auto_now=True, verbose_name=_("last access time"))
    access_count = models.PositiveIntegerField(default=0, verbose_name=_("access count"))

    # Pruning info
    last_pruned_at = models.DateTimeField(null=True, blank=True, verbose_name=_("last pruned at"))
    versions_pruned = models.PositiveIntegerField(default=0, verbose_name=_("versions pruned"))

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_("created at"))
    updated_at = models.DateTimeField(auto_now=True, verbose_name=_("updated at"))
    last_updated = models.DateTimeField(auto_now=True, verbose_name=_("last updated"))

    class Meta:
        app_label = "versioning"
        db_table: ClassVar = "versioning_metrics"
        verbose_name = _("version metrics")
        verbose_name_plural = _("version metrics")
        constraints: ClassVar = [
            models.UniqueConstraint(fields=["content_type", "object_id"], name="version_metrics_unique"),
        ]
        indexes: ClassVar = [
            models.Index(
                fields=["content_type", "total_versions"],
                name="version_metrics_content_idx",
            ),
            models.Index(fields=["last_access_time"], name="version_metrics_access_idx"),
            models.Index(fields=["total_storage_bytes"], name="version_metrics_storage_idx"),
        ]

    def __str__(self) -> str:
        return f"Metrics for {self.content_type.model} {self.object_id}"

    def update_metrics(self):
        """Update metrics based on current versions"""
        versions = UniversalVersion.objects.filter(content_type=self.content_type, object_id=self.object_id)

        metrics = versions.aggregate(
            total=Count("id"),
            significant=Count("id", filter=Q(is_significant=True)),
            total_size=Sum("data_size"),
            avg_compression=Avg("compression_ratio"),
            max_size=models.Max("data_size"),
            oldest_date=models.Min("created_at"),
        )

        self.total_versions = metrics["total"] or 0
        self.significant_versions = metrics["significant"] or 0
        self.total_storage_bytes = metrics["total_size"] or 0
        self.compressed_storage_bytes = int((metrics["total_size"] or 0) * (metrics["avg_compression"] or 1.0))
        self.avg_compression_ratio = metrics["avg_compression"] or 1.0
        self.largest_version_mb = (metrics["max_size"] or 0) / 1024 / 1024
        self.oldest_version_date = metrics["oldest_date"]

        self.save()

    def get_storage_savings(self) -> dict[str, Any]:
        """Get information about storage savings from compression"""
        savings_bytes = self.total_storage_bytes - self.compressed_storage_bytes
        savings_percent = (savings_bytes / self.total_storage_bytes * 100) if self.total_storage_bytes > 0 else 0

        return {
            "total_storage_bytes": self.total_storage_bytes,
            "compressed_storage_bytes": self.compressed_storage_bytes,
            "savings_bytes": savings_bytes,
            "savings_percent": savings_percent,
            "compression_ratio": self.avg_compression_ratio,
            "total_versions": self.total_versions,
            "significant_versions": self.significant_versions,
            "largest_version_mb": self.largest_version_mb,
            "oldest_version_date": self.oldest_version_date,
        }

    def increment_access(self):
        """Increment access counter"""
        self.access_count += 1
        self.save(update_fields=["access_count", "last_access_time"])


# Import new diff-based models for enhanced storage efficiency
