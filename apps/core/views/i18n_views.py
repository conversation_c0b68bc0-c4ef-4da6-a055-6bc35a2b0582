"""
Internationalization Views
Handles language switching and i18n functionality with HTMX integration

Created: 2025-07-28
Task: #48 - Add internationalization (i18n) support for multiple languages
"""

import logging
from typing import Any, Dict

from django.conf import settings
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin
from django.http import HttpRequest, HttpResponse, JsonResponse
from django.shortcuts import redirect, render
from django.urls import reverse
from django.utils import translation
from django.utils.decorators import method_decorator
from django.utils.translation import gettext as _
from django.utils.translation import get_language, get_language_info
from django.views import View
from django.views.decorators.cache import never_cache
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods, require_POST
from django.views.generic import TemplateView

logger = logging.getLogger(__name__)


class LanguageSwitchView(View):
    """Handle language switching with HTMX support."""

    def get(self, request: HttpRequest) -> HttpResponse:
        """Get current language information."""
        current_language = get_language()

        # Get available languages
        available_languages = []
        for lang_code, lang_name in settings.LANGUAGES:
            lang_info = get_language_info(lang_code)
            available_languages.append({
                'code': lang_code,
                'name': lang_name,
                'name_local': lang_info['name_local'],
                'bidi': lang_info['bidi'],
                'is_current': lang_code == current_language
            })

        # Return JSON response for API calls
        if request.headers.get('Accept') == 'application/json':
            return JsonResponse({
                'current_language': current_language,
                'available_languages': available_languages,
                'success': True
            })

        # Return HTML for HTMX requests
        context = {
            'current_language': current_language,
            'available_languages': available_languages,
            'LANGUAGES': settings.LANGUAGES
        }

        if request.htmx:
            template = 'core/partials/language_selector.html'
        else:
            template = 'core/language_selector.html'

        return render(request, template, context)

    def post(self, request: HttpRequest) -> HttpResponse:
        """Switch to a new language."""
        try:
            # Get the new language from request
            new_language = request.POST.get('language')

            # Validate language choice
            valid_languages = [lang[0] for lang in settings.LANGUAGES]
            if new_language not in valid_languages:
                return JsonResponse({
                    'success': False,
                    'error': _('Invalid language choice'),
                    'valid_languages': valid_languages
                }, status=400)

            # Get current language for logging
            old_language = get_language()

            # Activate the new language
            translation.activate(new_language)

            # Log language change
            logger.info(
                f"Language changed from {old_language} to {new_language}",
                extra={
                    'user_id': request.user.id if request.user.is_authenticated else None,
                    'old_language': old_language,
                    'new_language': new_language,
                    'ip_address': self._get_client_ip(request),
                    'user_agent': request.META.get('HTTP_USER_AGENT', '')
                }
            )

            # Prepare response data
            response_data = {
                'success': True,
                'language': new_language,
                'old_language': old_language,
                'message': _('Language changed successfully'),
                'language_name': dict(settings.LANGUAGES).get(new_language, new_language)
            }

            # Create response
            if request.headers.get('Accept') == 'application/json' or request.htmx:
                response = JsonResponse(response_data)
            else:
                # Regular form submission - redirect to prevent double submission
                next_url = request.POST.get('next', request.META.get('HTTP_REFERER', '/'))
                response = redirect(next_url)

            # Set language cookie
            response.set_cookie(
                settings.LANGUAGE_COOKIE_NAME,
                new_language,
                max_age=settings.LANGUAGE_COOKIE_AGE,
                path=settings.LANGUAGE_COOKIE_PATH,
                domain=settings.LANGUAGE_COOKIE_DOMAIN,
                secure=settings.LANGUAGE_COOKIE_SECURE,
                httponly=settings.LANGUAGE_COOKIE_HTTPONLY,
                samesite=settings.LANGUAGE_COOKIE_SAMESITE,
            )

            # Add language information to response headers for JavaScript
            if request.htmx:
                response['X-Clear-Language-Updated'] = new_language
                response['X-Clear-Language-Previous'] = old_language
                response['X-Clear-Language-Name'] = dict(settings.LANGUAGES).get(new_language, new_language)

            return response

        except Exception as e:
            logger.error(
                f"Error switching language: {str(e)}",
                extra={
                    'user_id': request.user.id if request.user.is_authenticated else None,
                    'error': str(e),
                    'ip_address': self._get_client_ip(request)
                },
                exc_info=True
            )

            error_response = {
                'success': False,
                'error': _('Failed to switch language'),
                'details': str(e) if settings.DEBUG else None
            }

            if request.headers.get('Accept') == 'application/json' or request.htmx:
                return JsonResponse(error_response, status=500)

            # For regular requests, redirect with error message
            return redirect(request.META.get('HTTP_REFERER', '/'))

    def _get_client_ip(self, request: HttpRequest) -> str:
        """Get client IP address from request."""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


@never_cache
@require_POST
def set_language(request: HttpRequest) -> HttpResponse:
    """
    Set language preference (Django's built-in view with HTMX enhancements).
    This is compatible with Django's standard set_language view but adds HTMX support.
    """
    try:
        language = request.POST.get('language')
        next_url = request.POST.get('next', request.META.get('HTTP_REFERER', '/'))

        # Validate language
        valid_languages = [lang[0] for lang in settings.LANGUAGES]
        if language not in valid_languages:
            if request.htmx:
                return JsonResponse({
                    'success': False,
                    'error': _('Invalid language choice')
                }, status=400)
            return redirect(next_url)

        # Get current language for comparison
        old_language = get_language()

        # Activate new language
        translation.activate(language)

        # Create response
        if request.htmx:
            response_data = {
                'success': True,
                'language': language,
                'old_language': old_language,
                'message': _('Language changed successfully'),
                'reload_required': True  # Indicate that page should be reloaded
            }
            response = JsonResponse(response_data)
        else:
            response = redirect(next_url)

        # Set language cookie
        response.set_cookie(
            settings.LANGUAGE_COOKIE_NAME,
            language,
            max_age=settings.LANGUAGE_COOKIE_AGE,
            path=settings.LANGUAGE_COOKIE_PATH,
            domain=settings.LANGUAGE_COOKIE_DOMAIN,
            secure=settings.LANGUAGE_COOKIE_SECURE,
            httponly=settings.LANGUAGE_COOKIE_HTTPONLY,
            samesite=settings.LANGUAGE_COOKIE_SAMESITE,
        )

        # Add HTMX headers
        if request.htmx:
            response['X-Clear-Language-Updated'] = language
            response['X-Clear-Language-Previous'] = old_language
            response['HX-Refresh'] = 'true'  # Tell HTMX to refresh the page

        return response

    except Exception as e:
        logger.error(f"Error in set_language view: {str(e)}", exc_info=True)

        if request.htmx:
            return JsonResponse({
                'success': False,
                'error': _('Failed to change language')
            }, status=500)

        return redirect(request.META.get('HTTP_REFERER', '/'))


@require_http_methods(["GET"])
def get_language_info(request: HttpRequest) -> HttpResponse:
    """Get comprehensive language information."""
    try:
        current_language = get_language()
        current_lang_info = get_language_info(current_language)

        # Build comprehensive language information
        languages_info = []
        for lang_code, lang_name in settings.LANGUAGES:
            lang_info = get_language_info(lang_code)
            languages_info.append({
                'code': lang_code,
                'name': lang_name,
                'name_local': lang_info['name_local'],
                'name_translated': _(lang_name),
                'bidi': lang_info['bidi'],
                'is_current': lang_code == current_language
            })

        response_data = {
            'success': True,
            'current_language': {
                'code': current_language,
                'name': dict(settings.LANGUAGES).get(current_language, current_language),
                'name_local': current_lang_info['name_local'],
                'bidi': current_lang_info['bidi']
            },
            'available_languages': languages_info,
            'language_cookie_name': settings.LANGUAGE_COOKIE_NAME,
            'rtl_languages': [lang['code'] for lang in languages_info if lang['bidi']],
            'total_languages': len(settings.LANGUAGES)
        }

        return JsonResponse(response_data)

    except Exception as e:
        logger.error(f"Error getting language info: {str(e)}", exc_info=True)

        return JsonResponse({
            'success': False,
            'error': _('Failed to get language information')
        }, status=500)


class LanguageSettingsView(TemplateView):
    """Full language settings page."""

    template_name = 'core/language_settings.html'

    def get_context_data(self, **kwargs) -> Dict[str, Any]:
        """Add language-related context."""
        context = super().get_context_data(**kwargs)

        current_language = get_language()
        current_lang_info = get_language_info(current_language)

        # Build language information
        languages_info = []
        for lang_code, lang_name in settings.LANGUAGES:
            lang_info = get_language_info(lang_code)
            languages_info.append({
                'code': lang_code,
                'name': lang_name,
                'name_local': lang_info['name_local'],
                'bidi': lang_info['bidi'],
                'is_current': lang_code == current_language,
                'flag_emoji': self._get_flag_emoji(lang_code)
            })

        context.update({
            'current_language': current_language,
            'current_language_info': current_lang_info,
            'available_languages': languages_info,
            'language_descriptions': {
                'en': _('English - International language of business and technology'),
                'es': _('Spanish - Spoken by over 500 million people worldwide'),
                'fr': _('French - Language of diplomacy and international relations'),
                'ar': _('Arabic - Spoken by over 400 million people across the Middle East and North Africa'),
            },
            'rtl_languages': [lang['code'] for lang in languages_info if lang['bidi']],
            'language_cookie_settings': {
                'name': settings.LANGUAGE_COOKIE_NAME,
                'age': settings.LANGUAGE_COOKIE_AGE,
                'path': settings.LANGUAGE_COOKIE_PATH,
            }
        })

        return context

    def _get_flag_emoji(self, lang_code: str) -> str:
        """Get flag emoji for language code."""
        flag_map = {
            'en': '🇺🇸',  # English (US flag)
            'es': '🇪🇸',  # Spanish (Spain flag)
            'fr': '🇫🇷',  # French (France flag)
            'ar': '🇸🇦',  # Arabic (Saudi Arabia flag)
            'de': '🇩🇪',  # German (Germany flag)
            'it': '🇮🇹',  # Italian (Italy flag)
            'pt': '🇵🇹',  # Portuguese (Portugal flag)
            'ru': '🇷🇺',  # Russian (Russia flag)
            'zh': '🇨🇳',  # Chinese (China flag)
            'ja': '🇯🇵',  # Japanese (Japan flag)
            'ko': '🇰🇷',  # Korean (South Korea flag)
        }
        return flag_map.get(lang_code, '🌐')


# Context processor for adding language information to all templates
def language_context_processor(request: HttpRequest) -> Dict[str, Any]:
    """Add language information to template context."""
    current_language = get_language()

    try:
        current_lang_info = get_language_info(current_language)
    except KeyError:
        # Fallback if language info is not available
        current_lang_info = {
            'name_local': current_language,
            'bidi': False
        }

    # Build available languages list
    available_languages = []
    for lang_code, lang_name in settings.LANGUAGES:
        try:
            lang_info = get_language_info(lang_code)
            available_languages.append({
                'code': lang_code,
                'name': lang_name,
                'name_local': lang_info['name_local'],
                'bidi': lang_info['bidi'],
                'is_current': lang_code == current_language
            })
        except KeyError:
            # Fallback for unknown language codes
            available_languages.append({
                'code': lang_code,
                'name': lang_name,
                'name_local': lang_name,
                'bidi': False,
                'is_current': lang_code == current_language
            })

    return {
        'current_language': current_language,
        'current_language_info': current_lang_info,
        'available_languages': available_languages,
        'is_rtl': current_lang_info['bidi'],
        'language_cookie_name': settings.LANGUAGE_COOKIE_NAME,
        'total_languages': len(settings.LANGUAGES)
    }


# Utility functions
def get_user_language_preference(request: HttpRequest) -> str:
    """Get user's language preference from various sources."""
    # 1. Check URL parameter
    if 'lang' in request.GET:
        lang = request.GET['lang']
        if lang in [code for code, name in settings.LANGUAGES]:
            return lang

    # 2. Check cookie
    if hasattr(request, 'COOKIES'):
        cookie_lang = request.COOKIES.get(settings.LANGUAGE_COOKIE_NAME)
        if cookie_lang and cookie_lang in [code for code, name in settings.LANGUAGES]:
            return cookie_lang

    # 3. Check user profile (if authenticated)
    if request.user.is_authenticated:
        try:
            profile = request.user.profile
            if hasattr(profile, 'language') and profile.language:
                return profile.language
        except AttributeError:
            pass

    # 4. Check Accept-Language header
    if hasattr(request, 'META'):
        accept_language = request.META.get('HTTP_ACCEPT_LANGUAGE', '')
        for lang_code, lang_name in settings.LANGUAGES:
            if lang_code in accept_language:
                return lang_code

    # 5. Fallback to default
    return settings.LANGUAGE_CODE


def is_rtl_language(language_code: str) -> bool:
    """Check if a language is right-to-left."""
    try:
        lang_info = get_language_info(language_code)
        return lang_info['bidi']
    except KeyError:
        # Fallback for known RTL languages
        rtl_languages = ['ar', 'he', 'fa', 'ur', 'ku']
        return language_code in rtl_languages
