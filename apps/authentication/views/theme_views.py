"""
Theme Management Views
Handles user theme preference switching with HTMX integration

Created: 2025-07-28
Task: #47 - Implement dark mode support throughout the application
"""

import json
import logging
from typing import Any, Dict

from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin
from django.http import HttpRequest, HttpResponse, JsonResponse
from django.shortcuts import get_object_or_404, render
from django.utils.decorators import method_decorator
from django.utils.translation import gettext as _
from django.views import View
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods, require_POST
from django.views.generic import TemplateView

from apps.authentication.models import User, UserProfile

logger = logging.getLogger(__name__)


class ThemePreferenceView(LoginRequiredMixin, View):
    """Handle theme preference changes via HTMX."""

    def get(self, request: HttpRequest) -> HttpResponse:
        """Get current theme preference."""
        try:
            profile = request.user.profile
            current_theme = profile.theme
        except UserProfile.DoesNotExist:
            # Create profile if it doesn't exist
            profile = UserProfile.objects.create(user=request.user)
            current_theme = profile.theme

        # Return JSON response for API calls
        if request.headers.get('Accept') == 'application/json':
            return JsonResponse({
                'theme': current_theme,
                'choices': dict(UserProfile.THEME_CHOICES),
                'success': True
            })

        # Return HTML for HTMX requests
        context = {
            'current_theme': current_theme,
            'theme_choices': UserProfile.THEME_CHOICES,
            'user': request.user
        }

        if request.htmx:
            template = 'authentication/partials/theme_selector.html'
        else:
            template = 'authentication/theme_preference.html'

        return render(request, template, context)

    def post(self, request: HttpRequest) -> HttpResponse:
        """Update theme preference."""
        try:
            # Get the new theme from request
            new_theme = request.POST.get('theme')

            # Validate theme choice
            valid_themes = [choice[0] for choice in UserProfile.THEME_CHOICES]
            if new_theme not in valid_themes:
                return JsonResponse({
                    'success': False,
                    'error': _('Invalid theme choice'),
                    'valid_choices': valid_themes
                }, status=400)

            # Get or create user profile
            profile, created = UserProfile.objects.get_or_create(user=request.user)

            # Update theme preference
            old_theme = profile.theme
            profile.theme = new_theme
            profile.save(update_fields=['theme', 'updated_at'])

            # Log theme change
            logger.info(
                f"User {request.user.email} changed theme from {old_theme} to {new_theme}",
                extra={
                    'user_id': request.user.id,
                    'old_theme': old_theme,
                    'new_theme': new_theme,
                    'ip_address': self._get_client_ip(request)
                }
            )

            # Prepare response data
            response_data = {
                'success': True,
                'theme': new_theme,
                'old_theme': old_theme,
                'message': _('Theme preference updated successfully'),
                'theme_display': dict(UserProfile.THEME_CHOICES).get(new_theme, new_theme)
            }

            # Return JSON response for API calls
            if request.headers.get('Accept') == 'application/json':
                return JsonResponse(response_data)

            # Return HTMX response
            if request.htmx:
                # Add theme information to response headers for JavaScript
                response = JsonResponse(response_data)
                response['X-Clear-Theme-Updated'] = new_theme
                response['X-Clear-Theme-Previous'] = old_theme
                return response

            # Regular form submission - redirect to prevent double submission
            from django.shortcuts import redirect
            return redirect('authentication:theme_preference')

        except Exception as e:
            logger.error(
                f"Error updating theme preference for user {request.user.email}: {str(e)}",
                extra={
                    'user_id': request.user.id,
                    'error': str(e),
                    'ip_address': self._get_client_ip(request)
                },
                exc_info=True
            )

            error_response = {
                'success': False,
                'error': _('Failed to update theme preference'),
                'details': str(e) if request.user.is_staff else None
            }

            if request.headers.get('Accept') == 'application/json' or request.htmx:
                return JsonResponse(error_response, status=500)

            # For regular requests, render error page
            return render(request, 'authentication/theme_preference.html', {
                'error': error_response['error'],
                'current_theme': getattr(request.user.profile, 'theme', 'auto'),
                'theme_choices': UserProfile.THEME_CHOICES,
                'user': request.user
            }, status=500)

    def _get_client_ip(self, request: HttpRequest) -> str:
        """Get client IP address from request."""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


@login_required
@require_POST
def toggle_theme(request: HttpRequest) -> HttpResponse:
    """Toggle between light and dark themes (HTMX endpoint)."""
    try:
        # Get or create user profile
        profile, created = UserProfile.objects.get_or_create(user=request.user)

        # Toggle logic: light -> dark -> auto -> light
        current_theme = profile.theme
        if current_theme == 'light':
            new_theme = 'dark'
        elif current_theme == 'dark':
            new_theme = 'auto'
        else:  # auto or any other value
            new_theme = 'light'

        # Update theme
        profile.theme = new_theme
        profile.save(update_fields=['theme', 'updated_at'])

        # Log theme toggle
        logger.info(
            f"User {request.user.email} toggled theme from {current_theme} to {new_theme}",
            extra={
                'user_id': request.user.id,
                'old_theme': current_theme,
                'new_theme': new_theme,
                'action': 'toggle',
                'ip_address': request.META.get('REMOTE_ADDR')
            }
        )

        response_data = {
            'success': True,
            'theme': new_theme,
            'old_theme': current_theme,
            'action': 'toggle',
            'message': _('Theme toggled to {}').format(dict(UserProfile.THEME_CHOICES).get(new_theme, new_theme))
        }

        # HTMX response with headers
        response = JsonResponse(response_data)
        response['X-Clear-Theme-Updated'] = new_theme
        response['X-Clear-Theme-Previous'] = current_theme
        response['X-Clear-Theme-Action'] = 'toggle'

        return response

    except Exception as e:
        logger.error(
            f"Error toggling theme for user {request.user.email}: {str(e)}",
            extra={'user_id': request.user.id, 'error': str(e)},
            exc_info=True
        )

        return JsonResponse({
            'success': False,
            'error': _('Failed to toggle theme'),
            'details': str(e) if request.user.is_staff else None
        }, status=500)


@login_required
@require_http_methods(["GET"])
def get_theme_info(request: HttpRequest) -> HttpResponse:
    """Get comprehensive theme information for the current user."""
    try:
        # Get user profile
        try:
            profile = request.user.profile
            current_theme = profile.theme
        except UserProfile.DoesNotExist:
            # Create profile with default theme
            profile = UserProfile.objects.create(user=request.user)
            current_theme = profile.theme

        # Prepare comprehensive theme information
        theme_info = {
            'success': True,
            'user_preference': current_theme,
            'theme_choices': dict(UserProfile.THEME_CHOICES),
            'available_themes': [choice[0] for choice in UserProfile.THEME_CHOICES],
            'theme_display_names': {
                choice[0]: str(choice[1]) for choice in UserProfile.THEME_CHOICES
            },
            'current_display_name': dict(UserProfile.THEME_CHOICES).get(current_theme, current_theme),
            'user_info': {
                'id': request.user.id,
                'email': request.user.email,
                'has_profile': True
            }
        }

        # Add system preference detection hint for frontend
        user_agent = request.META.get('HTTP_USER_AGENT', '')
        theme_info['client_hints'] = {
            'user_agent': user_agent,
            'supports_prefers_color_scheme': True  # Modern browsers support this
        }

        return JsonResponse(theme_info)

    except Exception as e:
        logger.error(
            f"Error getting theme info for user {request.user.email}: {str(e)}",
            extra={'user_id': request.user.id, 'error': str(e)},
            exc_info=True
        )

        return JsonResponse({
            'success': False,
            'error': _('Failed to get theme information'),
            'details': str(e) if request.user.is_staff else None
        }, status=500)


class ThemeSettingsView(LoginRequiredMixin, TemplateView):
    """Full theme settings page."""

    template_name = 'authentication/theme_settings.html'

    def get_context_data(self, **kwargs) -> Dict[str, Any]:
        """Add theme-related context."""
        context = super().get_context_data(**kwargs)

        try:
            profile = self.request.user.profile
        except UserProfile.DoesNotExist:
            profile = UserProfile.objects.create(user=self.request.user)

        context.update({
            'current_theme': profile.theme,
            'theme_choices': UserProfile.THEME_CHOICES,
            'theme_descriptions': {
                'light': _('Light theme with bright colors and dark text'),
                'dark': _('Dark theme with dark colors and light text'),
                'auto': _('Automatically switch based on your system preference')
            },
            'user_profile': profile
        })

        return context


# Utility function for template context processors
def get_user_theme_preference(user) -> str:
    """Get user's theme preference, with fallback to 'auto'."""
    if not user.is_authenticated:
        return 'auto'

    try:
        return user.profile.theme
    except (UserProfile.DoesNotExist, AttributeError):
        return 'auto'


# Context processor for adding theme information to all templates
def theme_context_processor(request: HttpRequest) -> Dict[str, Any]:
    """Add theme information to template context."""
    if not request.user.is_authenticated:
        return {
            'user_theme_preference': 'auto',
            'theme_choices': UserProfile.THEME_CHOICES,
            'theme_display_names': dict(UserProfile.THEME_CHOICES)
        }

    try:
        profile = request.user.profile
        current_theme = profile.theme
    except UserProfile.DoesNotExist:
        current_theme = 'auto'

    return {
        'user_theme_preference': current_theme,
        'theme_choices': UserProfile.THEME_CHOICES,
        'theme_display_names': dict(UserProfile.THEME_CHOICES),
        'current_theme_display': dict(UserProfile.THEME_CHOICES).get(current_theme, current_theme)
    }
