"""Infrastructure App Admin Configuration

Comprehensive admin interface for spatial models, utilities, and 3D model management.
"""

from django.contrib import admin
from django.contrib.admin import SimpleListFilter
from django.db import models
from django.forms import ModelForm, Textarea
from django.utils import timezone
from django.utils.html import format_html
from django.utils.safestring import mark_safe
from django.utils.translation import gettext_lazy as _

from apps.core.admin import CompositePrimaryKeyMixin

# Import models with graceful error handling
try:
    from .models import (
        AssetPerformanceProfile,
        AssetProcessingJob,
        AssetQualityMetrics,
        AssetUsageTracking,
        AssetWorkflowStep,
        CollaborationSession,
        CollaborativeDrawing,
        Conflict,
        CoordinateSystem,
        GISLayer,
        LineStyle,
        Location,
        ProjectAssetMapping,
        SpatialAnalytics,
        SpatialAnnotation,
        SpatialBookmark,
        SpatialBookmarkAccess,
        SpatialSearchSession,
        SpatialWorkflow,
        ThreeDAsset,
        ThreeDAssetLibrary,
        ThreeDAssetLOD,
        ThreeDAssetTexture,
        Utility,
        UtilityLineData,
        UtilityPhaseStatus,
        WorkflowBookmark,
    )

    CORE_MODELS_AVAILABLE = True
except ImportError:
    CORE_MODELS_AVAILABLE = False

# Import symbol models
try:
    from .symbol_library import UtilitySymbolLibrary
    from .symbol_models import (
        SymbolApprovalWorkflow,
        SymbolCategory,
        SymbolImportSession,
        SymbolLibrary,
        SymbolLibraryItem,
        SymbolPlacement,
        SymbolStandard,
        SymbolStandardCompliance,
        SymbolTemplate,
        SymbolUsageAnalytics,
        UtilitySymbol,
    )

    SYMBOL_MODELS_AVAILABLE = True
except ImportError:
    SYMBOL_MODELS_AVAILABLE = False

# Import 3D models
try:
    from .models import (
        ModelGenerationJob,
        ModelOptimizationProfile,
        ProceduralModelTemplate,
        ThreeDModelLibrary,
        UtilitySymbol3DMapping,
    )

    THREED_MODELS_AVAILABLE = True
except ImportError:
    THREED_MODELS_AVAILABLE = False


# =============================================================================
# CUSTOM FILTERS
# =============================================================================


class QualityLevelFilter(SimpleListFilter):
    """Filter for 3D model quality levels"""

    title = _("Quality Level")
    parameter_name = "quality"

    def lookups(self, request, model_admin):
        if THREED_MODELS_AVAILABLE:
            return getattr(ThreeDModelLibrary, "QUALITY_LEVELS", [])
        return []

    def queryset(self, request, queryset):
        if self.value():
            return queryset.filter(quality_level=self.value())
        return queryset


class ApprovalStatusFilter(SimpleListFilter):
    """Filter for model approval status"""

    title = _("Approval Status")
    parameter_name = "approval"

    def lookups(self, request, model_admin):
        return (
            ("approved", _("Approved")),
            ("pending", _("Pending Approval")),
            ("public", _("Public Models")),
            ("private", _("Private Models")),
        )

    def queryset(self, request, queryset):
        if self.value() == "approved":
            return queryset.filter(is_approved=True)
        if self.value() == "pending":
            return queryset.filter(is_approved=False)
        if self.value() == "public":
            return queryset.filter(is_public=True)
        if self.value() == "private":
            return queryset.filter(is_public=False)
        return queryset


# =============================================================================
# BASE ADMIN MIXINS
# =============================================================================


class BaseInfrastructureAdminMixin:
    """Base mixin for infrastructure admin classes with organization isolation"""

    def has_add_permission(self, request):
        return request.user.is_authenticated and request.user.has_perm(
            f"{self.model._meta.app_label}.add_{self.model._meta.model_name}",
        )

    def has_change_permission(self, request, obj=None):
        if not request.user.is_authenticated:
            return False
        if (
            obj
            and hasattr(obj, "organization")
            and hasattr(request.user, "organization")
            and obj.organization != request.user.organization
        ):
            return False
        return request.user.has_perm(f"{self.model._meta.app_label}.change_{self.model._meta.model_name}")

    def has_delete_permission(self, request, obj=None):
        if not request.user.is_authenticated:
            return False
        if (
            obj
            and hasattr(obj, "organization")
            and hasattr(request.user, "organization")
            and obj.organization != request.user.organization
        ):
            return False
        return request.user.has_perm(f"{self.model._meta.app_label}.delete_{self.model._meta.model_name}")

    def has_view_permission(self, request, obj=None):
        if not request.user.is_authenticated:
            return False
        if (
            obj
            and hasattr(obj, "organization")
            and hasattr(request.user, "organization")
            and obj.organization != request.user.organization
        ):
            return False
        return request.user.has_perm(f"{self.model._meta.app_label}.view_{self.model._meta.model_name}")

    def get_queryset(self, request):
        qs = super().get_queryset(request)
        if request.user.is_superuser:
            return qs
        # Filter by user's organization if applicable
        if hasattr(request.user, "organization") and hasattr(self.model, "organization"):
            return qs.filter(organization=request.user.organization)
        return qs


# =============================================================================
# SYMBOL MANAGEMENT ADMIN
# =============================================================================

if SYMBOL_MODELS_AVAILABLE:

    class UtilitySymbolAdminForm(ModelForm):
        """Custom form for utility symbol admin with SVG preview"""

        class Meta:
            model = UtilitySymbol
            fields = "__all__"
            widgets = {
                "svg_content": Textarea(attrs={"rows": 10, "cols": 80}),
                "description": Textarea(attrs={"rows": 4, "cols": 60}),
                "placement_rules": Textarea(attrs={"rows": 4, "cols": 60}),
                "attributes": Textarea(attrs={"rows": 4, "cols": 60}),
            }

    @admin.register(SymbolCategory)
    class SymbolCategoryAdmin(BaseInfrastructureAdminMixin, admin.ModelAdmin):
        """Admin interface for symbol categories"""

        list_display = [
            "name",
            "category_type",
            "utility_type",
            "parent",
            "symbol_count_display",
            "sort_order",
            "is_active",
        ]
        list_filter = ["category_type", "utility_type", "is_active", "created_at"]
        search_fields = ["name", "description", "utility_type"]
        ordering = ["category_type", "sort_order", "name"]
        readonly_fields = ["created_at", "updated_at"]

        fieldsets = (
            (
                _("Basic Information"),
                {"fields": ("name", "description", "category_type", "parent")},
            ),
            (
                _("APWA Compliance"),
                {
                    "fields": ("utility_type", "apwa_color"),
                    "description": _("APWA color will be auto-populated based on utility type"),
                },
            ),
            (_("Organization"), {"fields": ("sort_order", "is_active")}),
            (
                _("Symbol Defaults"),
                {
                    "fields": ("default_scale", "default_rotation"),
                    "classes": ("collapse",),
                },
            ),
        )

        def symbol_count_display(self, obj):
            """Display symbol count for category"""
            count = getattr(obj, "symbol_count", 0)
            if count > 0:
                return format_html('<span style="color: green; font-weight: bold;">{}</span>', count)
            return "0"

        symbol_count_display.short_description = _("Symbols")

        def save_model(self, request, obj, form, change):
            """Set created_by field for new objects"""
            if not change:
                obj.created_by = request.user
            super().save_model(request, obj, form, change)

    @admin.register(UtilitySymbol)
    class UtilitySymbolAdmin(BaseInfrastructureAdminMixin, admin.ModelAdmin):
        """Advanced admin interface for utility symbols with SVG preview"""

        form = UtilitySymbolAdminForm

        list_display = [
            "symbol_code",
            "name",
            "symbol_type",
            "utility_type",
            "category",
            "status",
            "usage_count",
            "svg_preview",
            "is_active",
        ]
        list_filter = [
            "symbol_type",
            "utility_type",
            "status",
            "is_active",
            "category__category_type",
            "created_at",
        ]
        search_fields = ["symbol_code", "name", "description", "keywords"]
        ordering = ["symbol_code"]
        list_select_related = ["category"]
        readonly_fields = [
            "svg_hash",
            "usage_count",
            "last_used",
            "svg_preview_display",
            "created_at",
            "updated_at",
            "approved_at",
        ]
        actions = ["approve_symbols", "reject_symbols"]

        fieldsets = (
            (
                _("Basic Information"),
                {
                    "fields": (
                        "symbol_code",
                        "name",
                        "description",
                        "symbol_type",
                        "category",
                    ),
                },
            ),
            (
                _("APWA Compliance"),
                {
                    "fields": ("utility_type", "apwa_color"),
                    "description": _("APWA color will be auto-populated based on utility type"),
                },
            ),
            (
                _("SVG Symbol Data"),
                {"fields": ("svg_content", "svg_viewbox", "svg_preview_display")},
            ),
            (
                _("Symbol Specifications"),
                {
                    "fields": (
                        "base_width",
                        "base_height",
                        "insertion_point",
                        "available_sizes",
                        "scale_factor",
                    ),
                },
            ),
            (
                _("Placement & Rules"),
                {
                    "fields": ("placement_rules", "conflict_buffer"),
                    "classes": ("collapse",),
                },
            ),
            (
                _("Metadata"),
                {"fields": ("attributes", "keywords"), "classes": ("collapse",)},
            ),
            (
                _("Workflow & Status"),
                {"fields": ("status", "version", "is_active", "is_template")},
            ),
            (
                _("Usage Analytics"),
                {"fields": ("usage_count", "last_used"), "classes": ("collapse",)},
            ),
            (
                _("Approval"),
                {"fields": ("approved_by", "approved_at"), "classes": ("collapse",)},
            ),
        )

        def svg_preview(self, obj):
            """Display SVG preview in list view"""
            if hasattr(obj, "svg_content") and obj.svg_content:
                return format_html(
                    '<div style="width: 32px; height: 32px; border: 1px solid #ccc;">{}</div>',
                    obj.svg_content,
                )
            return _("No SVG")

        svg_preview.short_description = _("Preview")

        def svg_preview_display(self, obj):
            """Display larger SVG preview in detail view"""
            if hasattr(obj, "svg_content") and obj.svg_content:
                return format_html(
                    '<div style="width: 100px; height: 100px; border: 1px solid #ccc; margin: 10px;">{}</div>',
                    obj.svg_content,
                )
            return _("No SVG content")

        svg_preview_display.short_description = _("SVG Preview")

        def approve_symbols(self, request, queryset):
            """Bulk approve symbols"""
            updated = queryset.update(status="approved", approved_by=request.user, approved_at=timezone.now())
            self.message_user(request, _(f"{updated} symbols were successfully approved."))

        approve_symbols.short_description = _("Approve selected symbols")

        def reject_symbols(self, request, queryset):
            """Bulk reject symbols"""
            updated = queryset.update(status="rejected")
            self.message_user(request, _(f"{updated} symbols were rejected."))

        reject_symbols.short_description = _("Reject selected symbols")

        def save_model(self, request, obj, form, change):
            """Set created_by field for new objects"""
            if not change:
                obj.created_by = request.user
            super().save_model(request, obj, form, change)

    @admin.register(SymbolLibrary)
    class SymbolLibraryAdmin(BaseInfrastructureAdminMixin, admin.ModelAdmin):
        """Admin interface for symbol libraries"""

        list_display = [
            "name",
            "library_type",
            "access_level",
            "symbol_count_display",
            "is_published",
            "download_count",
            "created_at",
        ]
        list_filter = ["library_type", "access_level", "is_published", "created_at"]
        search_fields = ["name", "description", "tags"]
        ordering = ["-created_at"]
        readonly_fields = ["download_count", "created_at", "updated_at", "published_at"]

        def symbol_count_display(self, obj):
            """Display symbol count for library"""
            count = getattr(obj, "symbol_count", 0)
            if count > 0:
                return format_html('<span style="color: green; font-weight: bold;">{}</span>', count)
            return "0"

        symbol_count_display.short_description = _("Symbols")

        def save_model(self, request, obj, form, change):
            """Set created_by field for new objects"""
            if not change:
                obj.created_by = request.user
            super().save_model(request, obj, form, change)

    # Register remaining symbol models with basic admin
    try:
        admin.site.register(SymbolStandard)
        admin.site.register(SymbolStandardCompliance)
        admin.site.register(SymbolLibraryItem)
        admin.site.register(SymbolPlacement)
        admin.site.register(SymbolTemplate)
        admin.site.register(SymbolApprovalWorkflow)
        admin.site.register(SymbolImportSession)
        admin.site.register(SymbolUsageAnalytics)
    except admin.sites.AlreadyRegistered:
        pass


# =============================================================================
# 3D MODEL MANAGEMENT ADMIN
# =============================================================================

if THREED_MODELS_AVAILABLE:

    @admin.register(ThreeDModelLibrary)
    class ThreeDModelLibraryAdmin(BaseInfrastructureAdminMixin, admin.ModelAdmin):
        """3D Model Library administration with proper Django 5.2 patterns"""

        list_display = [
            "name",
            "model_type",
            "utility_category",
            "quality_level",
            "triangle_count",
            "file_size_display",
            "is_approved",
            "is_public",
            "created_at",
            "thumbnail_preview",
        ]
        list_filter = [
            "model_type",
            "utility_category",
            QualityLevelFilter,
            ApprovalStatusFilter,
            "is_procedural",
            "is_instanced",
        ]
        search_fields = ["name", "description", "tags"]
        readonly_fields = [
            "triangle_count",
            "vertex_count",
            "file_size_bytes",
            "created_at",
            "updated_at",
            "thumbnail_preview",
            "gltf_info",
        ]
        actions = ["approve_models", "make_public", "make_private"]

        fieldsets = (
            (
                _("Basic Information"),
                {
                    "fields": (
                        "name",
                        "description",
                        "model_type",
                        "utility_category",
                        "tags",
                    ),
                },
            ),
            (_("Files"), {"fields": ("gltf_file", "thumbnail", "thumbnail_preview")}),
            (
                _("Model Properties"),
                {
                    "fields": (
                        "triangle_count",
                        "vertex_count",
                        "file_size_bytes",
                        "quality_level",
                        "real_world_scale",
                        "default_material_type",
                    ),
                },
            ),
            (
                _("Procedural Generation"),
                {
                    "fields": ("is_procedural", "procedural_config"),
                    "classes": ("collapse",),
                },
            ),
            (
                _("Optimization"),
                {"fields": ("lod_levels", "is_instanced"), "classes": ("collapse",)},
            ),
            (_("Permissions"), {"fields": ("is_public", "is_approved", "created_by")}),
            (
                _("Metadata"),
                {"fields": ("created_at", "updated_at"), "classes": ("collapse",)},
            ),
            (_("Technical Info"), {"fields": ("gltf_info",), "classes": ("collapse",)}),
        )

        def file_size_display(self, obj):
            """Display file size in human readable format"""
            if hasattr(obj, "file_size_bytes") and obj.file_size_bytes:
                size_mb = getattr(obj, "get_file_size_mb", lambda: obj.file_size_bytes / (1024 * 1024))()
                if size_mb > 10:
                    return format_html('<span style="color: red;">{:.1f} MB</span>', size_mb)
                if size_mb > 5:
                    return format_html('<span style="color: orange;">{:.1f} MB</span>', size_mb)
                return format_html('<span style="color: green;">{:.1f} MB</span>', size_mb)
            return "-"

        file_size_display.short_description = _("File Size")

        def thumbnail_preview(self, obj):
            """Display thumbnail preview"""
            if hasattr(obj, "thumbnail") and obj.thumbnail:
                return format_html(
                    '<img src="{}" style="max-width: 100px; max-height: 100px;" />',
                    obj.thumbnail.url,
                )
            return _("No thumbnail")

        thumbnail_preview.short_description = _("Preview")

        def gltf_info(self, obj):
            """Display GLTF file information"""
            if hasattr(obj, "gltf_file") and obj.gltf_file:
                info = f"""
                <strong>File:</strong> {obj.gltf_file.name}<br>
                <strong>Triangles:</strong> {getattr(obj, "triangle_count", 0):,}<br>
                <strong>Vertices:</strong> {getattr(obj, "vertex_count", 0):,}
                """
                return mark_safe(info)
            return _("No GLTF file")

        gltf_info.short_description = _("GLTF Information")

        def approve_models(self, request, queryset):
            """Batch approve models"""
            updated = queryset.update(is_approved=True)
            self.message_user(request, _(f"{updated} models approved."))

        approve_models.short_description = _("Approve selected models")

        def make_public(self, request, queryset):
            """Make models public"""
            updated = queryset.update(is_public=True)
            self.message_user(request, _(f"{updated} models made public."))

        make_public.short_description = _("Make selected models public")

        def make_private(self, request, queryset):
            """Make models private"""
            updated = queryset.update(is_public=False)
            self.message_user(request, _(f"{updated} models made private."))

        make_private.short_description = _("Make selected models private")

    @admin.register(UtilitySymbol3DMapping)
    class UtilitySymbol3DMappingAdmin(BaseInfrastructureAdminMixin, admin.ModelAdmin):
        """Admin for 3D symbol mapping"""

        list_display = [
            "utility_type",
            "symbol_name",
            "primary_model",
            "priority",
            "scale_factor",
            "is_active",
            "created_at",
        ]
        list_filter = ["utility_type", "is_active", "priority"]
        search_fields = ["utility_type", "symbol_name", "symbol_code"]
        autocomplete_fields = ["primary_model", "fallback_model"]

        fieldsets = (
            (
                _("Symbol Information"),
                {"fields": ("utility_type", "symbol_name", "symbol_code")},
            ),
            (
                _("3D Model Assignment"),
                {"fields": ("primary_model", "fallback_model", "priority")},
            ),
            (
                _("Transformation"),
                {"fields": ("scale_factor", "rotation_offset", "position_offset")},
            ),
            (
                _("Conditional Logic"),
                {
                    "fields": (
                        "size_range_min",
                        "size_range_max",
                        "depth_range_min",
                        "depth_range_max",
                    ),
                    "classes": ("collapse",),
                },
            ),
            (_("Status"), {"fields": ("is_active",)}),
        )

        def get_queryset(self, request):
            """Optimize query with select_related"""
            return super().get_queryset(request).select_related("primary_model")

    # Register other 3D model admin classes
    try:
        if "ProceduralModelTemplate" in globals():

            @admin.register(ProceduralModelTemplate)
            class ProceduralModelTemplateAdmin(BaseInfrastructureAdminMixin, admin.ModelAdmin):
                list_display = [
                    "name",
                    "generation_type",
                    "target_triangle_count",
                    "usage_count",
                    "is_active",
                    "created_at",
                ]
                list_filter = ["generation_type", "default_quality_level", "is_active"]
                search_fields = ["name", "description"]
                readonly_fields = ["usage_count", "created_at", "updated_at"]

        if "ModelGenerationJob" in globals():

            @admin.register(ModelGenerationJob)
            class ModelGenerationJobAdmin(BaseInfrastructureAdminMixin, admin.ModelAdmin):
                list_display = [
                    "template",
                    "status",
                    "requested_by",
                    "created_at",
                    "completed_at",
                ]
                list_filter = ["status", "template__generation_type", "created_at"]
                search_fields = ["template__name", "requested_by__username"]
                readonly_fields = ["created_at", "started_at", "completed_at"]

        if "ModelOptimizationProfile" in globals():

            @admin.register(ModelOptimizationProfile)
            class ModelOptimizationProfileAdmin(BaseInfrastructureAdminMixin, admin.ModelAdmin):
                list_display = [
                    "name",
                    "target_platform",
                    "max_triangle_count",
                    "max_file_size_mb",
                    "compression_level",
                    "is_default",
                    "is_active",
                ]
                list_filter = ["target_platform", "is_default", "is_active"]
                search_fields = ["name"]

    except (FileNotFoundError, PermissionError, OSError):
        pass


# =============================================================================
# CORE INFRASTRUCTURE ADMIN
# =============================================================================

if CORE_MODELS_AVAILABLE:

    @admin.register(Utility)
    class UtilityAdmin(BaseInfrastructureAdminMixin, admin.ModelAdmin):
        """Admin for utility management"""

        list_display = [
            "name",
            "type",
            "status",
            "project",
            "contact_name",
            "last_response",
        ]
        list_filter = ["type", "status", "project"]
        search_fields = ["name", "contact_name", "contact_email"]
        readonly_fields = ["created_at", "updated_at"]

    @admin.register(UtilityLineData)
    class UtilityLineDataAdmin(BaseInfrastructureAdminMixin, admin.ModelAdmin):
        """Admin for utility line data"""

        list_display = ["name", "utility_type", "line_type", "project", "created_by"]
        list_filter = ["utility_type", "line_type", "installation_type"]
        search_fields = ["name", "utility_type"]
        readonly_fields = ["created_at", "updated_at"]

    @admin.register(Conflict)
    class ConflictAdmin(BaseInfrastructureAdminMixin, admin.ModelAdmin):
        """Admin for conflict management"""

        list_display = [
            "description",
            "status",
            "priority",
            "project",
            "confidence_score",
            "created_at",
        ]
        list_filter = ["status", "priority", "detection_method", "conflict_type"]
        search_fields = ["description", "location"]
        readonly_fields = ["created_at", "updated_at", "detected_timestamp"]

    @admin.register(SpatialBookmark)
    class SpatialBookmarkAdmin(BaseInfrastructureAdminMixin, admin.ModelAdmin):
        """Admin for spatial bookmarks"""

        list_display = [
            "name",
            "bookmark_type",
            "created_by",
            "view_count",
            "is_featured",
        ]
        list_filter = ["bookmark_type", "is_featured", "is_template"]
        search_fields = ["name", "description", "tags"]
        readonly_fields = ["view_count", "last_accessed", "access_log", "created_at"]

    @admin.register(SpatialWorkflow)
    class SpatialWorkflowAdmin(BaseInfrastructureAdminMixin, admin.ModelAdmin):
        """Admin for spatial workflows"""

        list_display = ["name", "workflow_type", "status", "created_by", "project"]
        list_filter = ["workflow_type", "status"]
        search_fields = ["name", "description"]
        readonly_fields = ["created_at", "updated_at", "started_at", "completed_at"]

    @admin.register(SpatialAnalytics)
    class SpatialAnalyticsAdmin(BaseInfrastructureAdminMixin, admin.ModelAdmin):
        """Admin for spatial analytics"""

        list_display = ["metric_type", "date", "user", "project", "created_at"]
        list_filter = ["metric_type", "date", "project"]
        readonly_fields = ["created_at"]

    @admin.register(CoordinateSystem)
    class CoordinateSystemAdmin(BaseInfrastructureAdminMixin, admin.ModelAdmin):
        """Admin for coordinate systems"""

        list_display = ["name", "created_at"]
        search_fields = ["name", "type", "state"]
        list_filter = ["created_at"]
        date_hierarchy = "created_at"
        ordering = ["-created_at"]

    @admin.register(LineStyle)
    class LineStyleAdmin(BaseInfrastructureAdminMixin, admin.ModelAdmin):
        """Admin for line styles"""

        list_display = ["created_at", "updated_at"]
        search_fields = ["utility_type", "installation_type", "line_color"]
        list_filter = ["standard_811", "created_at", "updated_at"]
        date_hierarchy = "created_at"
        ordering = ["-created_at"]

    @admin.register(GISLayer)
    class GISLayerAdmin(BaseInfrastructureAdminMixin, admin.ModelAdmin):
        """Admin for GIS layers"""

        list_display = ["name", "created_at", "updated_at"]
        search_fields = ["name", "layer_type", "data_source"]
        list_filter = ["visibility", "is_public", "created_at", "updated_at"]
        date_hierarchy = "created_at"
        ordering = ["-created_at"]

    @admin.register(SpatialAnnotation)
    class SpatialAnnotationAdmin(BaseInfrastructureAdminMixin, admin.ModelAdmin):
        """Admin for spatial annotations"""

        list_display = ["created_at", "updated_at"]
        search_fields = ["annotation_type", "color", "annotation_text"]
        list_filter = ["is_visible", "is_persistent", "created_at", "updated_at"]
        date_hierarchy = "created_at"
        ordering = ["-created_at"]

    @admin.register(CollaborativeDrawing)
    class CollaborativeDrawingAdmin(BaseInfrastructureAdminMixin, admin.ModelAdmin):
        """Admin for collaborative drawings"""

        list_display = ["created_at", "updated_at"]
        search_fields = ["drawing_type"]
        list_filter = ["is_temporary", "is_locked", "created_at", "updated_at"]
        date_hierarchy = "created_at"
        ordering = ["-created_at"]

    @admin.register(Location)
    class LocationAdmin(BaseInfrastructureAdminMixin, admin.ModelAdmin):
        """Admin for locations"""

        list_display = ["name", "created_at", "updated_at", "is_active"]
        search_fields = ["name", "location_type", "address"]
        list_filter = ["is_active", "created_at", "updated_at", "project"]
        date_hierarchy = "created_at"
        ordering = ["-created_at"]

    # 3D Asset Admin Classes
    @admin.register(ThreeDAssetLibrary)
    class ThreeDAssetLibraryAdmin(BaseInfrastructureAdminMixin, admin.ModelAdmin):
        """Admin for 3D asset libraries"""

        list_display = ["name", "created_at", "updated_at"]
        search_fields = ["name", "library_type", "access_level"]
        list_filter = ["created_at", "updated_at", "created_by"]
        date_hierarchy = "created_at"
        ordering = ["-created_at"]

    @admin.register(ThreeDAsset)
    class ThreeDAssetAdmin(BaseInfrastructureAdminMixin, admin.ModelAdmin):
        """Admin for 3D assets"""

        list_display = ["name", "created_at", "updated_at"]
        search_fields = ["name", "asset_type", "category"]
        list_filter = ["has_textures", "uses_pbr", "optimization_applied", "created_at"]
        date_hierarchy = "created_at"
        ordering = ["-created_at"]

    @admin.register(ThreeDAssetLOD)
    class ThreeDAssetLODAdmin(BaseInfrastructureAdminMixin, admin.ModelAdmin):
        """Admin for 3D asset LOD"""

        list_display = ["created_at", "updated_at"]
        search_fields = ["generation_method"]
        list_filter = ["created_at", "updated_at", "parent_asset"]
        date_hierarchy = "created_at"
        ordering = ["-created_at"]

    @admin.register(ThreeDAssetTexture)
    class ThreeDAssetTextureAdmin(BaseInfrastructureAdminMixin, admin.ModelAdmin):
        """Admin for 3D asset textures"""

        list_display = ["name", "created_at", "updated_at"]
        search_fields = ["name", "texture_type", "compression_format"]
        list_filter = ["created_at", "updated_at", "asset"]
        date_hierarchy = "created_at"
        ordering = ["-created_at"]

    @admin.register(AssetProcessingJob)
    class AssetProcessingJobAdmin(BaseInfrastructureAdminMixin, admin.ModelAdmin):
        """Admin for asset processing jobs"""

        list_display = ["created_at"]
        search_fields = ["job_type", "status", "current_step"]
        list_filter = ["created_at", "started_at", "completed_at", "asset"]
        date_hierarchy = "created_at"
        ordering = ["-created_at"]

    @admin.register(AssetQualityMetrics)
    class AssetQualityMetricsAdmin(BaseInfrastructureAdminMixin, admin.ModelAdmin):
        """Admin for asset quality metrics"""

        list_display = ["analysis_version", "analysis_date"]
        search_fields = ["analysis_version"]
        list_filter = ["analysis_date", "asset"]
        date_hierarchy = "analysis_date"
        ordering = ["-analysis_date"]

    @admin.register(AssetUsageTracking)
    class AssetUsageTrackingAdmin(BaseInfrastructureAdminMixin, admin.ModelAdmin):
        """Admin for asset usage tracking"""

        list_display = [
            "context",
            "screen_resolution",
            "device_type",
            "started_at",
            "ended_at",
        ]
        search_fields = ["context", "screen_resolution", "device_type"]
        list_filter = ["started_at", "ended_at", "asset", "project"]
        date_hierarchy = "started_at"
        ordering = ["-started_at"]

    @admin.register(AssetPerformanceProfile)
    class AssetPerformanceProfileAdmin(BaseInfrastructureAdminMixin, admin.ModelAdmin):
        """Admin for asset performance profiles"""

        list_display = [
            "device_category",
            "browser",
            "texture_quality",
            "shadows_enabled",
            "anti_aliasing",
        ]
        search_fields = ["device_category", "browser", "texture_quality"]
        list_filter = [
            "shadows_enabled",
            "anti_aliasing",
            "profile_date",
            "last_updated",
        ]
        date_hierarchy = "profile_date"
        ordering = ["-profile_date"]

    @admin.register(AssetWorkflowStep)
    class AssetWorkflowStepAdmin(BaseInfrastructureAdminMixin, admin.ModelAdmin):
        """Admin for asset workflow steps"""

        list_display = ["created_at", "updated_at"]
        search_fields = ["step_type", "status", "notes"]
        list_filter = ["is_automated", "started_at", "completed_at", "created_at"]
        date_hierarchy = "created_at"
        ordering = ["-created_at"]

    @admin.register(ProjectAssetMapping)
    class ProjectAssetMappingAdmin(BaseInfrastructureAdminMixin, admin.ModelAdmin):
        """Admin for project asset mapping"""

        list_display = ["created_at", "updated_at"]
        search_fields = ["mapping_type", "tint_color"]
        list_filter = ["visibility", "created_at", "updated_at", "project"]
        date_hierarchy = "created_at"
        ordering = ["-created_at"]


# =============================================================================
# COMPOSITE PRIMARY KEY MODELS
# =============================================================================

if CORE_MODELS_AVAILABLE:

    class UtilityPhaseStatusAdmin(CompositePrimaryKeyMixin, BaseInfrastructureAdminMixin, admin.ModelAdmin):
        """Admin for utility phase status with composite primary key support"""

        composite_pk_fields = ["utility", "phase_name"]
        list_display = [
            "utility",
            "phase_name",
            "status",
            "status_date",
            "notes_preview",
        ]
        search_fields = ["phase_name", "status", "notes"]
        list_filter = ["status_date", "created_at", "updated_at", "utility"]
        date_hierarchy = "created_at"
        ordering = ["-created_at"]
        autocomplete_fields = ["utility"]
        readonly_fields = ["status_date"]

        def notes_preview(self, obj):
            """Show preview of notes"""
            return obj.notes[:50] + "..." if obj.notes and len(obj.notes) > 50 else obj.notes or ""

        notes_preview.short_description = _("Notes")

    class SpatialBookmarkAccessAdmin(CompositePrimaryKeyMixin, BaseInfrastructureAdminMixin, admin.ModelAdmin):
        """Admin for spatial bookmark access with composite primary key support"""

        composite_pk_fields = ["bookmark", "user"]
        list_display = ["bookmark", "user", "access_level", "granted_at", "granted_by"]
        list_filter = ["access_level", "granted_at"]
        search_fields = ["bookmark__name", "user__username"]
        autocomplete_fields = ["bookmark", "user", "granted_by"]
        readonly_fields = ["granted_at"]
        ordering = ["-granted_at"]

    class WorkflowBookmarkAdmin(CompositePrimaryKeyMixin, BaseInfrastructureAdminMixin, admin.ModelAdmin):
        """Admin for workflow bookmark with composite primary key support"""

        composite_pk_fields = ["workflow", "bookmark"]
        list_display = ["workflow", "bookmark", "is_required", "completed_at"]
        search_fields = ["completion_notes"]
        list_filter = ["is_required", "completed_at", "workflow", "bookmark"]
        date_hierarchy = "completed_at"
        ordering = ["-completed_at"]
        autocomplete_fields = ["workflow", "bookmark"]

    # Register models with composite primary keys manually
    try:
        admin.site.register(UtilityPhaseStatus, UtilityPhaseStatusAdmin)
        admin.site.register(SpatialBookmarkAccess, SpatialBookmarkAccessAdmin)
        admin.site.register(WorkflowBookmark, WorkflowBookmarkAdmin)
    except admin.sites.AlreadyRegistered:
        pass
    except (FileNotFoundError, PermissionError, OSError):
        pass


# =============================================================================
# IMPORT TEMPLATE ADMIN
# =============================================================================

# Import models for advanced file import with graceful error handling
try:
    from .models_import_template import ImportTemplate

    @admin.register(ImportTemplate)
    class ImportTemplateAdmin(admin.ModelAdmin):
        """Admin interface for advanced import templates."""

        list_display = [
            "name",
            "template_type",
            "source_type",
            "mapping_strategy",
            "version",
            "usage_count",
            "is_public",
            "is_system_template",
            "last_used_at",
            "created_by",
        ]

        list_filter = [
            "template_type",
            "source_type",
            "mapping_strategy",
            "is_public",
            "is_system_template",
            "created_at",
            "last_used_at",
        ]

        search_fields = [
            "name",
            "description",
            "tags",
            "created_by__username",
            "created_by__first_name",
            "created_by__last_name",
        ]

        readonly_fields = [
            "template_id",
            "usage_count",
            "last_used_at",
            "created_at",
            "updated_at",
            "field_mapping_summary",
            "template_summary_display",
        ]

        autocomplete_fields = ["created_by", "organization"]

        fieldsets = (
            (
                _("Basic Information"),
                {
                    "fields": (
                        "template_id",
                        "name",
                        "description",
                        "version",
                        "tags",
                    )
                },
            ),
            (
                _("Template Configuration"),
                {
                    "fields": (
                        "template_type",
                        "source_type",
                        "mapping_strategy",
                        "supported_formats",
                    )
                },
            ),
            (
                _("Schema Definition"),
                {
                    "fields": (
                        "source_schema",
                        "target_schema",
                    ),
                    "classes": ("collapse",),
                },
            ),
            (
                _("Field Mappings & Transformations"),
                {
                    "fields": (
                        "field_mappings",
                        "transformation_rules",
                        "field_mapping_summary",
                    ),
                    "classes": ("collapse",),
                },
            ),
            (
                _("Validation & Error Handling"),
                {
                    "fields": (
                        "validation_rules",
                        "default_values",
                        "duplicate_detection_config",
                        "error_handling_config",
                    ),
                    "classes": ("collapse",),
                },
            ),
            (
                _("Preview Configuration"),
                {
                    "fields": ("preview_config",),
                    "classes": ("collapse",),
                },
            ),
            (
                _("Permissions & Sharing"),
                {
                    "fields": (
                        "is_public",
                        "is_system_template",
                        "created_by",
                        "organization",
                    )
                },
            ),
            (
                _("Usage Statistics"),
                {
                    "fields": (
                        "usage_count",
                        "last_used_at",
                        "created_at",
                        "updated_at",
                        "template_summary_display",
                    ),
                    "classes": ("collapse",),
                },
            ),
        )

        def get_queryset(self, request):
            """Filter by organization for non-superusers."""
            qs = super().get_queryset(request)
            if not request.user.is_superuser:
                # Show user's own templates + public + system templates + organization templates
                qs = qs.filter(
                    models.Q(created_by=request.user)
                    | models.Q(is_public=True)
                    | models.Q(is_system_template=True)
                    | models.Q(organization=getattr(request.user, "organization", None))
                )
            return qs

        def field_mapping_summary(self, obj):
            """Display field mapping summary."""
            if not obj:
                return "-"

            try:
                summary = obj.get_field_mapping_summary()
                return format_html(
                    '<div style="font-size: 12px;">'
                    "<strong>Mappings:</strong> {total}<br>"
                    "<strong>Transformations:</strong> {transformations}<br>"
                    "<strong>Validations:</strong> {validations}"
                    "</div>",
                    total=summary["total_mappings"],
                    transformations=summary["transformation_count"],
                    validations=summary["validation_count"],
                )
            except Exception:
                return "-"

        field_mapping_summary.short_description = _("Field Mapping Summary")

        def template_summary_display(self, obj):
            """Display comprehensive template summary."""
            if not obj:
                return "-"

            try:
                summary = obj.get_template_summary()
                return format_html(
                    '<div style="font-size: 12px; max-width: 400px;">'
                    "<strong>Template ID:</strong> {id}<br>"
                    "<strong>Formats:</strong> {formats}<br>"
                    "<strong>Field Mappings:</strong> {mappings}<br>"
                    "<strong>Features:</strong> {features}<br>"
                    "<strong>Usage:</strong> {usage} times<br>"
                    "<strong>Last Used:</strong> {last_used}"
                    "</div>",
                    id=str(obj.template_id)[:8] + "...",
                    formats=(", ".join(summary["supported_formats"]) if summary["supported_formats"] else "All"),
                    mappings=f"{summary['field_mappings']['total_mappings']} mappings",
                    features=", ".join(
                        [
                            "Transformations" if summary["has_transformations"] else "",
                            "Validations" if summary["has_validations"] else "",
                            "Defaults" if summary["has_defaults"] else "",
                        ]
                    ).strip(", ")
                    or "Basic",
                    usage=summary["usage_count"],
                    last_used=(summary["last_used_at"][:10] if summary["last_used_at"] else "Never"),
                )
            except Exception:
                return "-"

        template_summary_display.short_description = _("Template Summary")

        def get_form(self, request, obj=None, **kwargs):
            """Customize form based on user permissions."""
            form = super().get_form(request, obj, **kwargs)

            # Set initial values for new templates
            if not obj:
                form.base_fields["created_by"].initial = request.user
                if hasattr(request.user, "organization"):
                    form.base_fields["organization"].initial = request.user.organization

            # Restrict system template editing to superusers
            if not request.user.is_superuser:
                if "is_system_template" in form.base_fields:
                    form.base_fields["is_system_template"].disabled = True

            return form

        def save_model(self, request, obj, form, change):
            """Set created_by on new objects."""
            if not change:
                obj.created_by = request.user
                if hasattr(request.user, "organization") and not obj.organization:
                    obj.organization = request.user.organization

            super().save_model(request, obj, form, change)

        def has_change_permission(self, request, obj=None):
            """Allow changes for owners, superusers, and organization members."""
            if not obj:
                return super().has_change_permission(request, obj)

            if request.user.is_superuser:
                return True

            if obj.created_by == request.user:
                return True

            if obj.organization and hasattr(request.user, "organization"):
                if request.user.organization == obj.organization:
                    return True

            return False

        def has_delete_permission(self, request, obj=None):
            """Allow deletion for owners and superusers only."""
            if not obj:
                return super().has_delete_permission(request, obj)

            if request.user.is_superuser:
                return True

            if obj.created_by == request.user and not obj.is_system_template:
                return True

            return False

        class Media:
            css = {"all": ("admin/css/infrastructure_import_template.css",)}
            js = ("admin/js/infrastructure_import_template.js",)

except ImportError:
    pass


# =============================================================================
# ADMIN SITE CUSTOMIZATION
# =============================================================================

admin.site.site_header = _("CLEAR Infrastructure Management")
admin.site.site_title = _("CLEAR Admin")
admin.site.index_title = _("Infrastructure, Symbols & 3D Model Management")
