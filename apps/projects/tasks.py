"""
Celery tasks for project and task management.

This module provides background tasks for:
- Recurring task generation
- Task automation
- Project updates
- Notification processing
"""

import logging
from datetime import timedelta

from django.contrib.auth import get_user_model
from django.db import models
from django.db import transaction
from django.utils import timezone

from celery import shared_task

from .models import RecurringTaskPattern, Task

User = get_user_model()
logger = logging.getLogger(__name__)


@shared_task(bind=True, max_retries=3)
def generate_recurring_tasks(self, pattern_id: int = None, force: bool = False) -> dict:
    """
    Generate recurring task instances based on active patterns.

    Args:
        pattern_id: Specific pattern ID to process (optional)
        force: Force generation even if not due yet

    Returns:
        Dictionary with generation results
    """
    try:
        logger.info(f"Starting recurring task generation (pattern_id={pattern_id}, force={force})")

        result = {
            "success": True,
            "processed_patterns": 0,
            "created_tasks": 0,
            "skipped_patterns": 0,
            "errors": [],
            "generated_at": timezone.now().isoformat(),
        }

        # Get patterns to process
        if pattern_id:
            patterns = RecurringTaskPattern.objects.filter(id=pattern_id, is_active=True)
        else:
            patterns = RecurringTaskPattern.objects.filter(is_active=True)

        # Filter patterns that are due for generation (unless forced)
        if not force:
            now = timezone.now()
            patterns = patterns.filter(models.Q(next_due__lte=now) | models.Q(next_due__isnull=True))

        for pattern in patterns:
            try:
                with transaction.atomic():
                    # Check if we need to generate tasks for this pattern
                    if pattern.max_occurrences:
                        current_count = pattern.task_instances.count()
                        if current_count >= pattern.max_occurrences:
                            result["skipped_patterns"] += 1
                            logger.info(
                                f"Pattern {pattern.name} has reached max occurrences ({pattern.max_occurrences})"
                            )
                            continue

                    # Check end date
                    if pattern.end_date and timezone.now() > pattern.end_date:
                        result["skipped_patterns"] += 1
                        logger.info(f"Pattern {pattern.name} has passed end date")
                        # Deactivate pattern
                        pattern.is_active = False
                        pattern.save(update_fields=["is_active"])
                        continue

                    # Generate next task
                    new_task = pattern.generate_next_task()

                    if new_task:
                        result["created_tasks"] += 1
                        logger.info(f"Created recurring task: {new_task.title} (ID: {new_task.id})")

                        # Update pattern's last generated and next due
                        pattern.last_generated = timezone.now()
                        pattern.update_next_due()

                        # Send notification to assigned users
                        from .notifications import send_recurring_task_notification

                        send_recurring_task_notification.delay(new_task.id)

                    result["processed_patterns"] += 1

            except Exception as e:
                error_msg = f"Error processing pattern {pattern.name}: {str(e)}"
                logger.error(error_msg)
                result["errors"].append(error_msg)

        logger.info(
            f"Recurring task generation completed: {result['created_tasks']} tasks created, "
            f"{result['processed_patterns']} patterns processed"
        )

        return result

    except Exception as e:
        logger.error(f"Error in generate_recurring_tasks: {str(e)}")

        # Retry on failure
        if self.request.retries < self.max_retries:
            logger.info(f"Retrying recurring task generation (attempt {self.request.retries + 1})")
            raise self.retry(countdown=300, exc=e)  # Retry after 5 minutes

        # Final failure
        return {
            "success": False,
            "error": str(e),
            "retries": self.request.retries,
            "failed_at": timezone.now().isoformat(),
        }


@shared_task(bind=True, max_retries=2)
def cleanup_recurring_tasks(self, days_old: int = 365) -> dict:
    """
    Clean up old completed recurring task instances.

    Args:
        days_old: Remove completed tasks older than this many days

    Returns:
        Dictionary with cleanup results
    """
    try:
        logger.info(f"Starting cleanup of recurring tasks older than {days_old} days")

        cutoff_date = timezone.now() - timedelta(days=days_old)

        # Find old completed recurring task instances
        old_tasks = Task.objects.filter(
            recurring_pattern__isnull=False,
            status="completed",
            updated_at__lt=cutoff_date,
        )

        result = {
            "success": True,
            "cleaned_tasks": 0,
            "errors": [],
            "cleaned_at": timezone.now().isoformat(),
        }

        # Count before deletion
        task_count = old_tasks.count()

        if task_count > 0:
            # Delete old tasks
            deleted_count, _ = old_tasks.delete()
            result["cleaned_tasks"] = deleted_count

            logger.info(f"Cleaned up {deleted_count} old recurring tasks")
        else:
            logger.info("No old recurring tasks found for cleanup")

        return result

    except Exception as e:
        logger.error(f"Error in cleanup_recurring_tasks: {str(e)}")

        # Retry on failure
        if self.request.retries < self.max_retries:
            logger.info(f"Retrying cleanup (attempt {self.request.retries + 1})")
            raise self.retry(countdown=600, exc=e)  # Retry after 10 minutes

        # Final failure
        return {
            "success": False,
            "error": str(e),
            "retries": self.request.retries,
            "failed_at": timezone.now().isoformat(),
        }


@shared_task
def send_recurring_task_notification(task_id: int) -> dict:
    """
    Send notification for newly created recurring task.

    Args:
        task_id: ID of the task to send notification for

    Returns:
        Dictionary with notification results
    """
    try:
        task = (
            Task.objects.select_related("project", "recurring_pattern", "created_by")
            .prefetch_related("assigned_to")
            .get(id=task_id)
        )

        result = {
            "success": True,
            "task_id": task_id,
            "notifications_sent": 0,
            "sent_at": timezone.now().isoformat(),
        }

        # Get notification recipients
        recipients = set()

        # Add assigned users
        for user in task.assigned_to.all():
            recipients.add(user)

        # Add task creator if different
        if task.created_by not in recipients:
            recipients.add(task.created_by)

        # Send notifications
        from apps.notifications.compatibility import create_notification

        for user in recipients:
            try:
                create_notification(
                    recipient=user,
                    notification_type="task_assignment",
                    title="Recurring Task Assigned",
                    message=f'You have been assigned a recurring task: "{task.name}"',
                    data={
                        "task_id": task.id,
                        "project_id": task.project.id,
                        "assigned_by": task.created_by.username,
                        "is_recurring": True,
                    },
                    related_object=task,
                )
                result["notifications_sent"] += 1

            except Exception as e:
                logger.warning(f"Failed to send notification to {user.email}: {str(e)}")

        logger.info(f"Sent {result['notifications_sent']} notifications for recurring task {task.title}")

        return result

    except Task.DoesNotExist:
        error_msg = f"Task with ID {task_id} not found"
        logger.error(error_msg)
        return {"success": False, "error": error_msg, "task_id": task_id}

    except Exception as e:
        error_msg = f"Error sending recurring task notification: {str(e)}"
        logger.error(error_msg)
        return {"success": False, "error": error_msg, "task_id": task_id}


@shared_task
def validate_recurring_patterns() -> dict:
    """
    Validate all recurring patterns and fix any issues.

    Returns:
        Dictionary with validation results
    """
    try:
        logger.info("Starting validation of recurring patterns")

        result = {
            "success": True,
            "validated_patterns": 0,
            "fixed_patterns": 0,
            "deactivated_patterns": 0,
            "errors": [],
            "validated_at": timezone.now().isoformat(),
        }

        patterns = RecurringTaskPattern.objects.filter(is_active=True)

        for pattern in patterns:
            try:
                result["validated_patterns"] += 1

                # Check if pattern has passed end date
                if pattern.end_date and timezone.now() > pattern.end_date:
                    pattern.is_active = False
                    pattern.save(update_fields=["is_active"])
                    result["deactivated_patterns"] += 1
                    logger.info(f"Deactivated expired pattern: {pattern.name}")
                    continue

                # Check max occurrences
                if pattern.max_occurrences:
                    current_count = pattern.task_instances.count()
                    if current_count >= pattern.max_occurrences:
                        pattern.is_active = False
                        pattern.save(update_fields=["is_active"])
                        result["deactivated_patterns"] += 1
                        logger.info(f"Deactivated pattern at max occurrences: {pattern.name}")
                        continue

                # Update next_due if needed
                if not pattern.next_due or pattern.next_due < timezone.now():
                    old_next_due = pattern.next_due
                    pattern.update_next_due()

                    if pattern.next_due != old_next_due:
                        result["fixed_patterns"] += 1
                        logger.info(f"Updated next_due for pattern: {pattern.name}")

            except Exception as e:
                error_msg = f"Error validating pattern {pattern.name}: {str(e)}"
                logger.error(error_msg)
                result["errors"].append(error_msg)

        logger.info(
            f"Pattern validation completed: {result['validated_patterns']} validated, "
            f"{result['fixed_patterns']} fixed, {result['deactivated_patterns']} deactivated"
        )

        return result

    except Exception as e:
        logger.error(f"Error in validate_recurring_patterns: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "failed_at": timezone.now().isoformat(),
        }


@shared_task
def update_task_statuses_in_series(pattern_id: int, task_id: int, new_status: str, update_future: bool = False) -> dict:
    """
    Update task status for a recurring series.

    Args:
        pattern_id: ID of the recurring pattern
        task_id: ID of the specific task being updated
        new_status: New status to set
        update_future: Whether to update future tasks in series

    Returns:
        Dictionary with update results
    """
    try:
        logger.info(f"Updating task status in series (pattern: {pattern_id}, task: {task_id})")

        pattern = RecurringTaskPattern.objects.get(id=pattern_id)
        task = Task.objects.get(id=task_id)

        result = {
            "success": True,
            "updated_tasks": 0,
            "pattern_id": pattern_id,
            "task_id": task_id,
            "new_status": new_status,
            "updated_at": timezone.now().isoformat(),
        }

        # Update the current task
        task.status = new_status
        task.save(update_fields=["status"])
        result["updated_tasks"] += 1

        if update_future:
            # Update future tasks in the series
            future_tasks = pattern.task_instances.filter(
                due_date__gt=task.due_date,
                status="pending",  # Only update pending tasks
            )

            updated_count = future_tasks.update(status=new_status)
            result["updated_tasks"] += updated_count

            logger.info(f"Updated {updated_count} future tasks in series")

        logger.info(f"Updated {result['updated_tasks']} tasks in recurring series")

        return result

    except (RecurringTaskPattern.DoesNotExist, Task.DoesNotExist) as e:
        error_msg = f"Pattern or task not found: {str(e)}"
        logger.error(error_msg)
        return {
            "success": False,
            "error": error_msg,
            "pattern_id": pattern_id,
            "task_id": task_id,
        }

    except Exception as e:
        error_msg = f"Error updating task statuses in series: {str(e)}"
        logger.error(error_msg)
        return {
            "success": False,
            "error": error_msg,
            "pattern_id": pattern_id,
            "task_id": task_id,
        }


# Periodic task that runs every 15 minutes to generate recurring tasks
@shared_task
def periodic_recurring_task_generation():
    """Periodic task to generate recurring tasks."""
    logger.info("Running periodic recurring task generation")
    return generate_recurring_tasks.delay().get()


# Daily cleanup task
@shared_task
def daily_recurring_task_maintenance():
    """Daily maintenance for recurring tasks."""
    logger.info("Running daily recurring task maintenance")

    # Validate patterns
    validate_result = validate_recurring_patterns.delay().get()

    # Cleanup old tasks (keep completed tasks for 1 year)
    cleanup_result = cleanup_recurring_tasks.delay(days_old=365).get()

    return {
        "validation": validate_result,
        "cleanup": cleanup_result,
        "completed_at": timezone.now().isoformat(),
    }
