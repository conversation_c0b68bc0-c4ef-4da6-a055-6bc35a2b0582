"""
Management command to generate recurring tasks.

This command can be used to manually trigger recurring task generation
or as part of a cron job if Celery Beat is not available.
"""

from django.core.management.base import BaseCommand, CommandError
from django.db import models
from django.db import transaction
from django.utils import timezone

from apps.projects.models import RecurringTaskPattern


class Command(BaseCommand):
    help = "Generate recurring tasks based on active patterns"

    def add_arguments(self, parser):
        parser.add_argument("--pattern-id", type=int, help="Generate tasks for specific pattern ID only")
        parser.add_argument("--force", action="store_true", help="Force generation even if not due yet")
        parser.add_argument(
            "--dry-run",
            action="store_true",
            help="Show what would be generated without actually creating tasks",
        )
        parser.add_argument(
            "--organization-id",
            type=int,
            help="Generate tasks for specific organization only",
        )

    def handle(self, *args, **options):
        pattern_id = options.get("pattern_id")
        force = options.get("force", False)
        dry_run = options.get("dry_run", False)
        organization_id = options.get("organization_id")

        self.stdout.write(
            self.style.SUCCESS(
                f"Starting recurring task generation (pattern_id={pattern_id}, force={force}, dry_run={dry_run})"
            )
        )

        try:
            # Get patterns to process
            patterns = RecurringTaskPattern.objects.filter(is_active=True)

            if pattern_id:
                patterns = patterns.filter(id=pattern_id)
                if not patterns.exists():
                    raise CommandError(f"Pattern with ID {pattern_id} not found or inactive")

            if organization_id:
                patterns = patterns.filter(organization_id=organization_id)

            # Filter patterns that are due for generation (unless forced)
            if not force:
                now = timezone.now()
                patterns = patterns.filter(models.Q(next_due__lte=now) | models.Q(next_due__isnull=True))

            if not patterns.exists():
                self.stdout.write(self.style.WARNING("No patterns found for processing"))
                return

            self.stdout.write(f"Found {patterns.count()} patterns to process")

            total_created = 0
            total_skipped = 0
            errors = []

            for pattern in patterns:
                self.stdout.write(f"\nProcessing pattern: {pattern.name}")

                try:
                    # Check if we need to generate tasks for this pattern
                    if pattern.max_occurrences:
                        current_count = pattern.task_instances.count()
                        if current_count >= pattern.max_occurrences:
                            self.stdout.write(
                                self.style.WARNING(f"  Skipped - reached max occurrences ({pattern.max_occurrences})")
                            )
                            total_skipped += 1
                            continue

                    # Check end date
                    if pattern.end_date and timezone.now() > pattern.end_date:
                        self.stdout.write(self.style.WARNING("  Skipped - passed end date"))
                        total_skipped += 1

                        if not dry_run:
                            # Deactivate pattern
                            pattern.is_active = False
                            pattern.save(update_fields=["is_active"])
                            self.stdout.write("  Pattern deactivated")
                        continue

                    # Check what would be generated
                    next_due = pattern.get_next_occurrence()
                    if not next_due:
                        self.stdout.write(self.style.WARNING("  No next occurrence calculated"))
                        total_skipped += 1
                        continue

                    # Find template task
                    template_task = (
                        pattern.task_instances.filter(is_recurring_template=True).first()
                        or pattern.task_instances.order_by("occurrence_number").first()
                    )

                    if not template_task:
                        self.stdout.write(self.style.WARNING("  No template task found"))
                        total_skipped += 1
                        continue

                    self.stdout.write(f"  Next occurrence: {next_due}")
                    self.stdout.write(f"  Template task: {template_task.title}")

                    if dry_run:
                        self.stdout.write(self.style.SUCCESS("  Would create new task instance"))
                        total_created += 1
                    else:
                        # Generate next task
                        with transaction.atomic():
                            new_task = pattern.generate_next_task(template_task)

                            if new_task:
                                total_created += 1
                                self.stdout.write(
                                    self.style.SUCCESS(f"  Created task: {new_task.title} (ID: {new_task.id})")
                                )

                                # Update pattern's last generated and next due
                                pattern.last_generated = timezone.now()
                                pattern.update_next_due()
                            else:
                                self.stdout.write(self.style.WARNING("  Failed to create task"))
                                total_skipped += 1

                except Exception as e:
                    error_msg = f"Error processing pattern {pattern.name}: {str(e)}"
                    self.stdout.write(self.style.ERROR(f"  {error_msg}"))
                    errors.append(error_msg)

            # Summary
            self.stdout.write("\n" + "=" * 50)
            self.stdout.write(
                self.style.SUCCESS(
                    f"Generation completed:\n"
                    f"  Tasks {'would be ' if dry_run else ''}created: {total_created}\n"
                    f"  Patterns skipped: {total_skipped}\n"
                    f"  Errors: {len(errors)}"
                )
            )

            if errors:
                self.stdout.write("\nErrors encountered:")
                for error in errors:
                    self.stdout.write(self.style.ERROR(f"  - {error}"))

            if dry_run:
                self.stdout.write(
                    self.style.WARNING(
                        "\nThis was a dry run. No tasks were actually created. Run without --dry-run to create tasks."
                    )
                )

        except Exception as e:
            raise CommandError(f"Failed to generate recurring tasks: {str(e)}")
