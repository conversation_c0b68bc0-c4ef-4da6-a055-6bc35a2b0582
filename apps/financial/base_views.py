"""
Financial app core views.

Provides time tracking, billing, invoice management, and financial
dashboard functionality with HTMX integration for dynamic interfaces.
"""

import csv
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Any

from django.contrib import messages
from django.contrib.auth import get_user_model
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin
from django.db import models
from django.db.models import Avg, Case, Coalesce, Count, DecimalField, F, Max, Q, QuerySet, Sum, Value, When
from django.http import HttpRequest, HttpResponse, JsonResponse
from django.shortcuts import get_object_or_404, redirect, render
from django.urls import reverse_lazy
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.views.decorators.http import require_http_methods
from django.views.decorators.vary import vary_on_headers
from django.views.generic import (
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    DeleteView,
    DetailView,
    ListView,
    TemplateView,
    UpdateView,
)

from apps.common.mixins import (
    HTMXResponseMixin,
    OrganizationAccessMixin,
    RoleRequiredMixin,
)
from apps.core.decorators.access_control import requires_organization_access
from apps.projects.models import Project

from .models import (
    Budget,
    Expense,
    Invoice,
    TimeEntry,
    TimesheetPeriod,
    WorkType,
)


@login_required
def time_tracking_dashboard(request: HttpRequest) -> HttpResponse:
    """
    Main time tracking dashboard with role-based access.

    Displays current timer status, recent entries, and time summaries
    for today and this week with project filtering.
    """
    from apps.common.utils.permissions import filter_projects_by_access

    # Get projects accessible to the user
    accessible_projects = filter_projects_by_access(request.user, Project.objects.all())

    # Get active timer and recent entries
    active_timer = TimeEntry.get_active_timer(request.user)
    recent_entries = (
        TimeEntry.objects.filter(user=request.user).select_related("project", "work_type").order_by("-start_time")[:10]
    )

    # Calculate today's total time
    today = timezone.now().date()
    today_entries = TimeEntry.objects.filter(user=request.user, start_time__date=today)
    today_total = today_entries.aggregate(total=Sum("duration_hours"))["total"] or Decimal("0.00")

    # Calculate this week's total time
    start_of_week = today - timedelta(days=today.weekday())
    week_entries = TimeEntry.objects.filter(user=request.user, start_time__date__gte=start_of_week)
    week_total = week_entries.aggregate(total=Sum("duration_hours"))["total"] or Decimal("0.00")

    context = {
        "page_title": _("Time Tracking Dashboard"),
        "active_timer": active_timer,
        "recent_entries": recent_entries,
        "projects": accessible_projects,
        "work_types": WorkType.objects.filter(is_active=True, organization=request.user.organization),
        "today_total_hours": today_total,
        "week_total_hours": week_total,
    }

    return render(request, "financial/dashboard.html", context)


@login_required
def start_timer(request: HttpRequest) -> HttpResponse:
    """
    Start a new timer for time tracking.

    Stops any existing active timers and creates a new timer entry
    with HTMX support for dynamic updates.
    """
    if request.method == "POST":
        project_id = request.POST.get("project")
        work_type_id = request.POST.get("work_type")
        description = request.POST.get("description", "")

        if not project_id or not work_type_id:
            messages.error(request, _("Project and work type are required."))
            return redirect("financial:time_tracking")

        # Stop any existing active timers
        TimeEntry.stop_all_active_timers(request.user)

        # Create new timer entry
        project = get_object_or_404(Project, id=project_id)
        work_type = get_object_or_404(WorkType, id=work_type_id)

        time_entry = TimeEntry.objects.create(
            user=request.user,
            project=project,
            work_type=work_type,
            start_time=timezone.now(),
            description=description,
            is_timer_based=True,
            is_active_timer=True,
        )
        time_entry.start_timer()

        messages.success(request, _("Timer started for {project}").format(project=project.name))

        # Return HTMX partial if requested
        if request.headers.get("HX-Request"):
            return render(
                request,
                "financial/partials/timer_started.html",
                {"active_timer": time_entry},
            )

    return redirect("financial:time_tracking")


@login_required
def stop_timer(request: HttpRequest) -> HttpResponse:
    """
    Stop the active timer and calculate duration.

    Finds the active timer, stops it, calculates duration,
    and returns appropriate response with HTMX support.
    """
    if request.method == "POST":
        active_timer = TimeEntry.get_active_timer(request.user)

        if active_timer:
            active_timer.stop_timer()
            duration_hours = active_timer.duration_hours or Decimal("0.00")

            messages.success(
                request,
                _("Timer stopped. Duration: {hours}h").format(hours=duration_hours),
            )

            # Return HTMX partial if requested
            if request.headers.get("HX-Request"):
                return render(
                    request,
                    "financial/partials/timer_stopped.html",
                    {"last_entry": active_timer},
                )
        else:
            messages.warning(request, _("No active timer found."))

    return redirect("financial:time_tracking")


@login_required
def get_timer_status(request: HttpRequest) -> HttpResponse:
    """
    Get current timer status for HTMX polling.

    Returns timer display partial with current status,
    used for real-time timer updates.
    """
    active_timer = TimeEntry.get_active_timer(request.user)
    return render(request, "financial/partials/timer_display.html", {"active_timer": active_timer})


@login_required
def get_active_timer(request: HttpRequest) -> HttpResponse:
    """
    Get current active timer information as JSON.

    Returns JSON data about the currently running timer,
    used for API endpoints and JavaScript integration.
    """
    from django.http import JsonResponse

    try:
        active_timer = TimeEntry.get_active_timer(request.user)

        if active_timer:
            # Calculate elapsed time
            from django.utils import timezone

            elapsed = timezone.now() - active_timer.start_time
            elapsed_seconds = int(elapsed.total_seconds())

            data = {
                "has_active_timer": True,
                "timer_id": str(active_timer.id),
                "project_name": (active_timer.project.name if active_timer.project else "No Project"),
                "task_name": active_timer.task.name if active_timer.task else "No Task",
                "work_type": active_timer.work_type,
                "start_time": active_timer.start_time.isoformat(),
                "elapsed_seconds": elapsed_seconds,
                "elapsed_display": f"{elapsed_seconds // 3600:02d}:{(elapsed_seconds % 3600) // 60:02d}:{elapsed_seconds % 60:02d}",
                "description": active_timer.description or "",
                "is_billable": active_timer.is_billable,
            }
        else:
            data = {
                "has_active_timer": False,
                "timer_id": None,
                "elapsed_seconds": 0,
            }

        return JsonResponse(data)

    except Exception as e:
        return JsonResponse(
            {
                "error": str(e),
                "has_active_timer": False,
            },
            status=500,
        )


class TimeEntryListView(
    HTMXResponseMixin,
    LoginRequiredMixin,
    RoleRequiredMixin,
    ListView,
    OrganizationAccessMixin,
):
    """
    List view for time entries with filtering and pagination.

    Displays user's time entries with project and date filtering,
    optimized queries, and HTMX support for dynamic updates.
    """

    required_roles = ["stakeholder"]

    model = TimeEntry
    template_name = "financial/time_entries.html"
    context_object_name = "time_entries"
    paginate_by = 25

    def get_queryset(self) -> QuerySet[TimeEntry]:
        """Get filtered and optimized time entries queryset."""
        queryset = TimeEntry.objects.filter(user=self.request.user).select_related("project", "work_type", "task")

        # Filter by project
        project_id = self.request.GET.get("project")
        if project_id:
            queryset = queryset.filter(project_id=project_id)

        # Filter by work type
        work_type_id = self.request.GET.get("work_type")
        if work_type_id:
            queryset = queryset.filter(work_type_id=work_type_id)

        # Filter by date range
        start_date = self.request.GET.get("start_date")
        end_date = self.request.GET.get("end_date")
        if start_date:
            queryset = queryset.filter(start_time__date__gte=start_date)
        if end_date:
            queryset = queryset.filter(start_time__date__lte=end_date)

        # Filter by billing status
        billing_status = self.request.GET.get("billing_status")
        if billing_status:
            queryset = queryset.filter(billing_status=billing_status)

        return queryset.order_by("-start_time")

    def get_context_data(self, **kwargs: Any) -> dict[str, Any]:
        """Add additional context for the template."""
        context = super().get_context_data(**kwargs)

        context.update(
            {
                "page_title": _("Time Entries"),
                "projects": Project.objects.filter(team_members=self.request.user),
                "work_types": WorkType.objects.filter(organization=self.request.user.organization, is_active=True),
                "total_hours": self.get_queryset().aggregate(total=Sum("duration_hours"))["total"] or Decimal("0.00"),
            }
        )

        return context


class TimeEntryCreateView(
    HTMXResponseMixin,
    LoginRequiredMixin,
    RoleRequiredMixin,
    CreateView,
    OrganizationAccessMixin,
):
    """
    Create view for manual time entries.

    Allows users to manually create time entries with validation
    and automatic user assignment.
    """

    required_roles = ["utility-coordinator"]

    model = TimeEntry
    template_name = "financial/time_entry_form.html"
    fields = [
        "project",
        "task",
        "work_type",
        "start_time",
        "end_time",
        "duration_hours",
        "description",
        "notes",
        "hourly_rate",
        "is_billable",
    ]
    success_url = reverse_lazy("financial:time_entries")

    def form_valid(self, form):
        """Set user and calculate duration if needed."""
        form.instance.user = self.request.user

        # Calculate duration if start and end times are provided
        if form.instance.start_time and form.instance.end_time:
            duration, _ = form.instance.calculate_duration()
            if duration:
                form.instance.duration_hours = duration

        messages.success(self.request, _("Time entry created successfully."))

        return super().form_valid(form)

    def get_context_data(self, **kwargs: Any) -> dict[str, Any]:
        """Add additional context for the template."""
        context = super().get_context_data(**kwargs)
        context["page_title"] = _("Add Time Entry")
        return context


class TimeEntryUpdateView(
    HTMXResponseMixin,
    LoginRequiredMixin,
    RoleRequiredMixin,
    UpdateView,
    OrganizationAccessMixin,
):
    """
    Update view for time entries.

    Allows users to edit their own time entries with validation
    and duration recalculation.
    """

    required_roles = ["utility-coordinator"]

    model = TimeEntry
    template_name = "financial/time_entry_form.html"
    fields = [
        "project",
        "task",
        "work_type",
        "start_time",
        "end_time",
        "duration_hours",
        "description",
        "notes",
        "hourly_rate",
        "is_billable",
    ]
    success_url = reverse_lazy("financial:time_entries")

    def get_queryset(self) -> QuerySet[TimeEntry]:
        """Restrict to user's own time entries."""
        return TimeEntry.objects.filter(user=self.request.user)

    def form_valid(self, form):
        """Recalculate duration if times changed."""
        if form.instance.start_time and form.instance.end_time:
            duration, _ = form.instance.calculate_duration()
            if duration:
                form.instance.duration_hours = duration

        messages.success(self.request, _("Time entry updated successfully."))

        return super().form_valid(form)

    def get_context_data(self, **kwargs: Any) -> dict[str, Any]:
        """Add additional context for the template."""
        context = super().get_context_data(**kwargs)
        context["page_title"] = _("Edit Time Entry")
        return context


class TimeEntryDetailView(
    HTMXResponseMixin,
    LoginRequiredMixin,
    RoleRequiredMixin,
    DetailView,
    OrganizationAccessMixin,
):
    """
    Detail view for time entries.

    Shows detailed time entry information with edit/delete options
    for entries the user has access to.
    """

    required_roles = ["stakeholder"]

    model = TimeEntry
    template_name = "financial/time_entry_detail.html"
    context_object_name = "time_entry"

    def get_queryset(self) -> QuerySet[TimeEntry]:
        """Filter time entries by organization and user access."""
        return TimeEntry.objects.filter(
            organization=self.request.user.organization, user=self.request.user
        ).select_related("project", "task", "user")

    def get_context_data(self, **kwargs: Any) -> dict[str, Any]:
        context = super().get_context_data(**kwargs)
        context["page_title"] = (
            f"Time Entry Details - {self.object.project.name if self.object.project else 'No Project'}"
        )
        context["can_edit"] = self.object.user == self.request.user
        return context


class TimeEntryDeleteView(
    HTMXResponseMixin,
    LoginRequiredMixin,
    RoleRequiredMixin,
    DeleteView,
    OrganizationAccessMixin,
):
    """
    Delete view for time entries.

    Allows users to delete their own time entries with confirmation.
    """

    required_roles = ["utility-coordinator"]

    model = TimeEntry
    template_name = "financial/time_entry_confirm_delete.html"
    success_url = reverse_lazy("financial:time_entries")

    def get_queryset(self) -> QuerySet[TimeEntry]:
        """Filter time entries by organization and user access."""
        return TimeEntry.objects.filter(organization=self.request.user.organization, user=self.request.user)

    def delete(self, request, *args, **kwargs):
        """Handle deletion with success message."""
        self.object = self.get_object()
        success_url = self.get_success_url()

        # Log the deletion for audit purposes
        # from django.utils import timezone
        # import logging
        # logger = logging.getLogger(__name__)
        # logger.info(
        #     f"Time entry deleted: {self.object.id} by user {request.user.email}"
        # )

        self.object.delete()

        if request.headers.get("HX-Request"):
            # HTMX request - return partial template
            return render(
                request,
                "financial/partials/time_entry_deleted.html",
                {"message": "Time entry deleted successfully"},
            )
        # Regular request - redirect
        return redirect(success_url)


@login_required
@require_http_methods(["POST"])
def bulk_update_time_entries(request: HttpRequest) -> HttpResponse:
    """
    Bulk update multiple time entries.

    Handles bulk operations like updating billable status,
    work type, or project assignment for multiple entries.
    """
    from django.db import transaction
    from django.http import JsonResponse

    try:
        # Get the list of time entry IDs and update data
        entry_ids = request.POST.getlist("entry_ids[]")
        update_type = request.POST.get("update_type")

        if not entry_ids or not update_type:
            return JsonResponse({"error": "Missing entry IDs or update type"}, status=400)

        # Filter entries to only those the user owns
        entries = TimeEntry.objects.filter(id__in=entry_ids, organization=request.user.organization, user=request.user)

        updated_count = 0

        with transaction.atomic():
            if update_type == "billable":
                is_billable = request.POST.get("is_billable") == "true"
                updated_count = entries.update(is_billable=is_billable)

            elif update_type == "work_type":
                work_type = request.POST.get("work_type")
                if work_type:
                    updated_count = entries.update(work_type=work_type)

            elif update_type == "project":
                project_id = request.POST.get("project_id")
                if project_id:
                    # Verify user has access to the project
                    try:
                        from apps.projects.models import Project

                        project = Project.objects.get(id=project_id, organization=request.user.organization)
                        updated_count = entries.update(project=project)
                    except Project.DoesNotExist:
                        return JsonResponse({"error": "Invalid project selected"}, status=400)

            elif update_type == "delete":
                updated_count = entries.count()
                entries.delete()

            else:
                return JsonResponse({"error": "Invalid update type"}, status=400)

        return JsonResponse(
            {
                "success": True,
                "updated_count": updated_count,
                "message": f"Successfully updated {updated_count} time entries",
            }
        )

    except Exception:
        # from django.utils import timezone
        # import logging
        # logger = logging.getLogger(__name__)
        # logger.error(f"Error in bulk update: {str(e)}")
        return JsonResponse({"error": "An error occurred during bulk update"}, status=500)


@login_required
def timesheet_summary(request: HttpRequest) -> HttpResponse:
    """
    Display timesheet summary for the current user.

    Shows time entries grouped by week with totals,
    submission status, and filtering options.
    """
    from datetime import timedelta

    user = request.user
    organization = user.organization

    # Get date range for current period
    today = timezone.now().date()
    week_start = today - timedelta(days=today.weekday())
    week_end = week_start + timedelta(days=6)

    # Get time entries for the current week
    time_entries = (
        TimeEntry.objects.filter(
            user=user,
            organization=organization,
            start_time__date__gte=week_start,
            start_time__date__lte=week_end,
        )
        .select_related("project", "task")
        .order_by("start_time")
    )

    # Calculate totals
    total_hours = time_entries.aggregate(total=Sum("duration_hours"))["total"] or 0
    billable_hours = time_entries.filter(is_billable=True).aggregate(total=Sum("duration_hours"))["total"] or 0

    context = {
        "time_entries": time_entries,
        "week_start": week_start,
        "week_end": week_end,
        "total_hours": total_hours,
        "billable_hours": billable_hours,
        "non_billable_hours": total_hours - billable_hours,
    }

    return render(request, "financial/timesheet_summary.html", context)


@login_required
def timesheet_period_detail(request: HttpRequest, period_id: str) -> HttpResponse:
    """
    Display detailed timesheet information for a specific period.

    Shows all time entries, totals, and submission status
    for the specified timesheet period.
    """
    from django.http import Http404

    try:
        # For now, we'll use the period_id as a week identifier
        # In a full implementation, you'd have a TimesheetPeriod model
        from datetime import timedelta

        # Basic period handling - in practice you'd have a proper model
        user = request.user
        organization = user.organization

        # Get current week as default
        today = timezone.now().date()
        week_start = today - timedelta(days=today.weekday())
        week_end = week_start + timedelta(days=6)

        # Get time entries for the period
        time_entries = (
            TimeEntry.objects.filter(
                user=user,
                organization=organization,
                start_time__date__gte=week_start,
                start_time__date__lte=week_end,
            )
            .select_related("project", "task")
            .order_by("start_time")
        )

        # Calculate totals
        total_hours = time_entries.aggregate(total=Sum("duration_hours"))["total"] or 0
        billable_hours = time_entries.filter(is_billable=True).aggregate(total=Sum("duration_hours"))["total"] or 0

        context = {
            "period_id": period_id,
            "time_entries": time_entries,
            "week_start": week_start,
            "week_end": week_end,
            "total_hours": total_hours,
            "billable_hours": billable_hours,
            "non_billable_hours": total_hours - billable_hours,
            "can_submit": total_hours > 0,
        }

        return render(request, "financial/timesheet_period_detail.html", context)

    except Exception:
        raise Http404("Timesheet period not found")


@login_required
@require_http_methods(["POST"])
def submit_timesheet(request: HttpRequest) -> HttpResponse:
    """
    Submit timesheet for approval.

    Marks time entries as submitted and sends notification
    to supervisors for approval.
    """
    from django.db import transaction
    from django.http import JsonResponse

    try:
        request.POST.get("period_id")
        entry_ids = request.POST.getlist("entry_ids[]")

        if not entry_ids:
            return JsonResponse({"error": "No time entries selected for submission"}, status=400)

        user = request.user
        organization = user.organization

        with transaction.atomic():
            # Get time entries to submit
            entries = TimeEntry.objects.filter(id__in=entry_ids, user=user, organization=organization)

            if not entries.exists():
                return JsonResponse({"error": "No valid time entries found"}, status=400)

            # Mark entries as submitted
            submitted_count = entries.update(status="submitted", submitted_at=timezone.now())

            # In a full implementation, you'd send notifications here
            # send_timesheet_submission_notification(user, entries)

        return JsonResponse(
            {
                "success": True,
                "submitted_count": submitted_count,
                "message": f"Successfully submitted {submitted_count} time entries",
            }
        )

    except Exception:
        return JsonResponse({"error": "An error occurred during submission"}, status=500)


@login_required
def export_timesheet_csv(request: HttpRequest) -> HttpResponse:
    """
    Export timesheet data as CSV file.

    Generates CSV file with time entries for the specified period
    with all relevant details for reporting.
    """
    import csv
    from datetime import timedelta

    from django.http import HttpResponse

    user = request.user
    organization = user.organization

    # Get date range from query parameters
    start_date = request.GET.get("start_date")
    end_date = request.GET.get("end_date")

    if start_date and end_date:
        try:
            start_date = datetime.strptime(start_date, "%Y-%m-%d").date()
            end_date = datetime.strptime(end_date, "%Y-%m-%d").date()
        except ValueError:
            # Default to current week
            today = timezone.now().date()
            start_date = today - timedelta(days=today.weekday())
            end_date = start_date + timedelta(days=6)
    else:
        # Default to current week
        today = timezone.now().date()
        start_date = today - timedelta(days=today.weekday())
        end_date = start_date + timedelta(days=6)

    # Get time entries
    time_entries = (
        TimeEntry.objects.filter(
            user=user,
            organization=organization,
            start_time__date__gte=start_date,
            start_time__date__lte=end_date,
        )
        .select_related("project", "task")
        .order_by("start_time")
    )

    # Create CSV response
    response = HttpResponse(content_type="text/csv")
    response["Content-Disposition"] = f'attachment; filename="timesheet_{start_date}_to_{end_date}.csv"'

    writer = csv.writer(response)

    # Write header
    writer.writerow(
        [
            "Date",
            "Project",
            "Task",
            "Work Type",
            "Start Time",
            "End Time",
            "Duration (Hours)",
            "Description",
            "Billable",
            "Hourly Rate",
            "Total Amount",
        ]
    )

    # Write data
    for entry in time_entries:
        writer.writerow(
            [
                entry.start_time.date(),
                entry.project.name if entry.project else "No Project",
                entry.task.name if entry.task else "No Task",
                entry.work_type,
                entry.start_time.strftime("%H:%M"),
                entry.end_time.strftime("%H:%M") if entry.end_time else "Running",
                entry.duration_hours,
                entry.description or "",
                "Yes" if entry.is_billable else "No",
                entry.hourly_rate or 0,
                ((entry.duration_hours * (entry.hourly_rate or 0)) if entry.is_billable else 0),
            ]
        )

    return response


@login_required
def export_timesheet_json(request: HttpRequest) -> HttpResponse:
    """
    Export timesheet data as JSON file.

    Generates JSON file with time entries for the specified period
    with all relevant details for API integration.
    """
    from datetime import timedelta

    from django.http import JsonResponse

    user = request.user
    organization = user.organization

    # Get date range from query parameters
    start_date = request.GET.get("start_date")
    end_date = request.GET.get("end_date")

    if start_date and end_date:
        try:
            start_date = datetime.strptime(start_date, "%Y-%m-%d").date()
            end_date = datetime.strptime(end_date, "%Y-%m-%d").date()
        except ValueError:
            # Default to current week
            today = timezone.now().date()
            start_date = today - timedelta(days=today.weekday())
            end_date = start_date + timedelta(days=6)
    else:
        # Default to current week
        today = timezone.now().date()
        start_date = today - timedelta(days=today.weekday())
        end_date = start_date + timedelta(days=6)

    # Get time entries
    time_entries = (
        TimeEntry.objects.filter(
            user=user,
            organization=organization,
            start_time__date__gte=start_date,
            start_time__date__lte=end_date,
        )
        .select_related("project", "task")
        .order_by("start_time")
    )

    # Calculate totals
    total_hours = time_entries.aggregate(total=Sum("duration_hours"))["total"] or 0
    billable_hours = time_entries.filter(is_billable=True).aggregate(total=Sum("duration_hours"))["total"] or 0

    # Format data
    entries_data = []
    for entry in time_entries:
        entries_data.append(
            {
                "id": str(entry.id),
                "date": entry.start_time.date().isoformat(),
                "project": {
                    "id": str(entry.project.id) if entry.project else None,
                    "name": entry.project.name if entry.project else None,
                },
                "task": {
                    "id": str(entry.task.id) if entry.task else None,
                    "name": entry.task.name if entry.task else None,
                },
                "work_type": entry.work_type,
                "start_time": entry.start_time.isoformat(),
                "end_time": entry.end_time.isoformat() if entry.end_time else None,
                "duration_hours": float(entry.duration_hours),
                "description": entry.description or "",
                "is_billable": entry.is_billable,
                "hourly_rate": float(entry.hourly_rate or 0),
                "total_amount": (float(entry.duration_hours * (entry.hourly_rate or 0)) if entry.is_billable else 0),
            }
        )

    data = {
        "period": {
            "start_date": start_date.isoformat(),
            "end_date": end_date.isoformat(),
        },
        "user": {
            "id": str(user.id),
            "email": user.email,
            "name": f"{user.first_name} {user.last_name}".strip() or user.email,
        },
        "organization": {"id": str(organization.id), "name": organization.name},
        "summary": {
            "total_entries": len(entries_data),
            "total_hours": float(total_hours),
            "billable_hours": float(billable_hours),
            "non_billable_hours": float(total_hours - billable_hours),
        },
        "entries": entries_data,
    }

    return JsonResponse(data, json_dumps_params={"indent": 2})


@login_required
def financial_reports(request: HttpRequest) -> HttpResponse:
    """
    Financial reports dashboard.

    Displays monthly totals, billable hours, and project statistics
    for the current user.
    """
    # Calculate monthly totals for the user
    this_month = timezone.now().replace(day=1).date()
    monthly_entries = TimeEntry.objects.filter(user=request.user, start_time__date__gte=this_month)

    monthly_hours = monthly_entries.aggregate(total=Sum("duration_hours"))["total"] or Decimal("0.00")

    monthly_billable = monthly_entries.filter(is_billable=True).aggregate(total=Sum("duration_hours"))[
        "total"
    ] or Decimal("0.00")

    context = {
        "page_title": _("Financial Reports"),
        "user_projects": Project.objects.filter(team_members=request.user),
        "monthly_hours": monthly_hours,
        "monthly_billable": monthly_billable,
        "monthly_entries_count": monthly_entries.count(),
        "billable_percentage": ((monthly_billable / monthly_hours * 100) if monthly_hours > 0 else 0),
    }

    return render(request, "financial/reports.html", context)


class InvoiceListView(
    HTMXResponseMixin,
    LoginRequiredMixin,
    RoleRequiredMixin,
    ListView,
    OrganizationAccessMixin,
):
    """
    List view for invoices with project filtering.

    Shows invoices for projects the user is a member of,
    with status filtering and pagination.
    """

    required_roles = ["stakeholder"]

    model = Invoice
    template_name = "financial/invoices.html"
    context_object_name = "invoices"
    paginate_by = 25

    def get_queryset(self) -> QuerySet[Invoice]:
        """Get invoices for user's projects."""
        user_projects = Project.objects.filter(team_members=self.request.user)

        queryset = Invoice.objects.filter(project__in=user_projects).select_related("project", "created_by")

        # Filter by status if provided
        status = self.request.GET.get("status")
        if status:
            queryset = queryset.filter(status=status)

        return queryset.order_by("-invoice_date")

    def get_context_data(self, **kwargs: Any) -> dict[str, Any]:
        """Add additional context for the template."""
        context = super().get_context_data(**kwargs)
        context["page_title"] = _("Invoices")
        return context


class InvoiceDetailView(
    HTMXResponseMixin,
    LoginRequiredMixin,
    RoleRequiredMixin,
    DetailView,
    OrganizationAccessMixin,
):
    """
    Detail view for invoices.

    Shows detailed invoice information for invoices
    associated with user's projects.
    """

    required_roles = ["stakeholder"]

    model = Invoice
    template_name = "financial/invoice_detail.html"
    context_object_name = "invoice"

    def get_queryset(self) -> QuerySet[Invoice]:
        """Restrict to invoices for user's projects."""
        user_projects = Project.objects.filter(team_members=self.request.user)
        return Invoice.objects.filter(project__in=user_projects).select_related("project", "created_by")

    def get_context_data(self, **kwargs: Any) -> dict[str, Any]:
        context = super().get_context_data(**kwargs)
        context["page_title"] = "Invoice Details"
        return context


class InvoiceCreateView(
    HTMXResponseMixin,
    LoginRequiredMixin,
    RoleRequiredMixin,
    CreateView,
    OrganizationAccessMixin,
):
    """
    Create view for invoices.

    Allows users to create new invoices with proper organization
    association and validation.
    """

    required_roles = ["department-manager"]

    model = Invoice
    template_name = "financial/invoice_form.html"
    fields = [
        "project",
        "invoice_number",
        "description",
        "amount",
        "due_date",
        "status",
        "notes",
    ]
    success_url = reverse_lazy("financial:invoices")

    def form_valid(self, form):
        """Set organization and user before saving."""
        form.instance.organization = self.request.user.organization
        form.instance.created_by = self.request.user
        return super().form_valid(form)

    def get_form(self, form_class=None):
        """Filter project choices by organization."""
        form = super().get_form(form_class)
        if hasattr(form.fields, "project"):
            from apps.projects.models import Project

            form.fields["project"].queryset = Project.objects.filter(organization=self.request.user.organization)
        return form

    def get_context_data(self, **kwargs: Any) -> dict[str, Any]:
        context = super().get_context_data(**kwargs)
        context["page_title"] = "Create Invoice"
        return context


class InvoiceUpdateView(
    HTMXResponseMixin,
    LoginRequiredMixin,
    RoleRequiredMixin,
    UpdateView,
    OrganizationAccessMixin,
):
    """
    Update view for invoices.

    Allows users to edit invoices with proper organization
    filtering and validation.
    """

    required_roles = ["department-manager"]

    model = Invoice
    template_name = "financial/invoice_form.html"
    fields = [
        "project",
        "invoice_number",
        "description",
        "amount",
        "due_date",
        "status",
        "notes",
    ]
    success_url = reverse_lazy("financial:invoices")

    def get_queryset(self) -> QuerySet[Invoice]:
        """Filter invoices by organization."""
        return Invoice.objects.filter(organization=self.request.user.organization)

    def get_form(self, form_class=None):
        """Filter project choices by organization."""
        form = super().get_form(form_class)
        if hasattr(form.fields, "project"):
            from apps.projects.models import Project

            form.fields["project"].queryset = Project.objects.filter(organization=self.request.user.organization)
        return form

    def get_context_data(self, **kwargs: Any) -> dict[str, Any]:
        context = super().get_context_data(**kwargs)
        context["page_title"] = f"Edit Invoice - {self.object.invoice_number}"
        return context


class InvoicePDFView(LoginRequiredMixin, RoleRequiredMixin, DetailView, OrganizationAccessMixin):
    """
    PDF generation view for invoices.

    Generates PDF version of invoice for download or email.
    """

    required_roles = ["stakeholder"]

    model = Invoice
    template_name = "financial/invoice_pdf.html"

    def get_queryset(self) -> QuerySet[Invoice]:
        """Filter invoices by organization."""
        return Invoice.objects.filter(organization=self.request.user.organization).select_related(
            "project", "organization"
        )

    def render_to_response(self, context, **response_kwargs):
        """Render invoice as PDF."""
        from django.http import HttpResponse
        from django.template.loader import render_to_string

        try:
            # For now, return HTML that can be converted to PDF
            # In production, you'd use a library like WeasyPrint or ReportLab
            template_name = self.template_name or "financial/invoice_pdf.html"
            html_content = render_to_string(template_name, context)

            response = HttpResponse(html_content, content_type="text/html")
            response["Content-Disposition"] = f'inline; filename="invoice_{self.object.invoice_number}.html"'

            # Add PDF-specific styling
            response.write(
                """
            <style>
                @media print {
                    body { font-family: Arial, sans-serif; }
                    .no-print { display: none; }
                }
            </style>
            """
            )

            return response

        except Exception:
            # Fallback to regular template response
            return super().render_to_response(context, **response_kwargs)


@login_required
@require_http_methods(["POST"])
def send_invoice(request: HttpRequest, pk: str) -> HttpResponse:
    """
    Send invoice via email.

    Sends invoice PDF to the specified email address
    with proper organization filtering.
    """
    from django.http import JsonResponse
    from django.shortcuts import get_object_or_404

    try:
        # Get invoice with organization filtering
        invoice = get_object_or_404(Invoice, pk=pk, organization=request.user.organization)

        email_address = request.POST.get("email")
        if not email_address:
            return JsonResponse({"error": "Email address is required"}, status=400)

        # In a full implementation, you'd send the actual email here
        # send_invoice_email(invoice, email_address, request.user)

        # For now, just simulate success
        return JsonResponse(
            {
                "success": True,
                "message": f"Invoice {invoice.invoice_number} sent to {email_address}",
            }
        )

    except Invoice.DoesNotExist:
        return JsonResponse({"error": "Invoice not found"}, status=404)
    except Exception:
        return JsonResponse({"error": "An error occurred while sending the invoice"}, status=500)


@login_required
@require_http_methods(["POST"])
def mark_invoice_paid(request: HttpRequest, pk: str) -> HttpResponse:
    """
    Mark invoice as paid.

    Updates invoice status to paid and records payment date
    with proper organization filtering.
    """
    from django.http import JsonResponse
    from django.shortcuts import get_object_or_404

    try:
        # Get invoice with organization filtering
        invoice = get_object_or_404(Invoice, pk=pk, organization=request.user.organization)

        # Check if user has permission to mark as paid
        if not request.user.has_role_in_organization(request.user.organization, "department_manager"):
            return JsonResponse(
                {"error": "Insufficient permissions to mark invoice as paid"},
                status=403,
            )

        # Update invoice status
        invoice.status = "paid"
        invoice.save()

        # Log the action for audit purposes
        # create_audit_log(request.user, 'invoice_paid', invoice)

        return JsonResponse(
            {
                "success": True,
                "message": f"Invoice {invoice.invoice_number} marked as paid",
                "new_status": invoice.status,
            }
        )

    except Invoice.DoesNotExist:
        return JsonResponse({"error": "Invoice not found"}, status=404)
    except Exception:
        return JsonResponse({"error": "An error occurred while updating the invoice"}, status=500)


class FinancialDashboardView(
    HTMXResponseMixin,
    LoginRequiredMixin,
    RoleRequiredMixin,
    TemplateView,
    OrganizationAccessMixin,
):
    """
    Enhanced financial dashboard with comprehensive analytics and KPIs.

    Shows revenue analysis, cost breakdowns, profitability metrics, budget performance,
    expense tracking, AR aging, and Chart.js visualizations with drill-down capabilities.
    """

    required_roles = ["department-manager"]
    template_name = "financial/dashboard.html"

    def get_context_data(self, **kwargs: Any) -> dict[str, Any]:
        """Build comprehensive financial dashboard context with enhanced KPIs."""
        from datetime import date

        from apps.common.utils.permissions import (
            can_access_financial_data,
            filter_projects_by_access,
        )

        context = super().get_context_data(**kwargs)
        user = self.request.user

        # Get accessible projects for organization-aware filtering
        accessible_projects = filter_projects_by_access(user, Project.objects.filter(organization=user.organization))
        project_ids = list(accessible_projects.values_list("id", flat=True))

        # Enhanced date ranges for comprehensive analysis
        today = timezone.now().date()
        start_of_week = today - timedelta(days=today.weekday())
        start_of_month = today.replace(day=1)
        quarter_start = date(today.year, ((today.month - 1) // 3) * 3 + 1, 1)
        year_start = date(today.year, 1, 1)
        last_month_start = (start_of_month - timedelta(days=1)).replace(day=1)
        last_month_end = start_of_month - timedelta(days=1)

        if project_ids and can_access_financial_data(user):
            # Core Financial KPIs
            context.update(self._get_core_financial_metrics(project_ids, start_of_month, today))

            # Revenue Analysis with trends
            context.update(self._get_revenue_analysis(project_ids, start_of_month, last_month_start, last_month_end))

            # Enhanced Cost Analysis including expenses
            context.update(self._get_enhanced_cost_analysis(project_ids, start_of_month, quarter_start))

            # Profitability Analysis by project/client
            context.update(self._get_profitability_analysis(project_ids, start_of_month))

            # Budget Performance vs Actuals
            context.update(self._get_budget_performance_analysis(project_ids))

            # AR Aging and Cash Flow Analysis
            context.update(self._get_ar_aging_analysis(project_ids))

            # Team Utilization and Efficiency Metrics
            context.update(self._get_utilization_metrics(project_ids, start_of_month, start_of_week))

            # Chart.js Data for Financial Trends
            context.update(self._get_chart_data_sets(project_ids, start_of_month, quarter_start))

            # Legacy compatibility - maintaining existing template structure
            context.update(self._get_legacy_compatibility_context(user, start_of_week, start_of_month, project_ids))

        else:
            context.update(self._get_empty_dashboard_context())

        # Enhanced metadata for filtering and drill-down
        context.update(
            {
                "today": today,
                "week_start": start_of_week,
                "month_start": start_of_month,
                "quarter_start": quarter_start,
                "year_start": year_start,
                "show_financial_data": can_access_financial_data(user),
                "accessible_projects": accessible_projects[:10],  # For filtering dropdown
                "page_title": _("Financial Dashboard"),
            }
        )

        return context

    def _get_core_financial_metrics(self, project_ids, month_start, today):
        """Calculate core financial KPIs for the dashboard."""
        from django.db.models import Coalesce, DecimalField, Value

        # Total revenue (billable time + invoice amounts)
        billable_revenue = TimeEntry.objects.filter(
            project__in=project_ids,
            start_time__date__gte=month_start,
            start_time__date__lte=today,
            is_billable=True,
        ).aggregate(
            total=Coalesce(
                Sum(F("duration_hours") * F("hourly_rate")),
                Value(0, output_field=DecimalField()),
            )
        )[
            "total"
        ] or Decimal(
            "0"
        )

        # Total expenses (approved expenses only)
        total_expenses = Expense.objects.filter(
            project__in=project_ids,
            expense_date__gte=month_start,
            expense_date__lte=today,
            approval_status="approved",
        ).aggregate(total=Coalesce(Sum("amount"), Value(0, output_field=DecimalField())))["total"] or Decimal("0")

        # Gross margin calculation
        gross_margin = billable_revenue - total_expenses
        margin_percentage = (gross_margin / billable_revenue * 100) if billable_revenue > 0 else 0

        # Outstanding AR
        outstanding_ar = Invoice.objects.filter(project__in=project_ids, status__in=["sent", "overdue"]).aggregate(
            total=Coalesce(Sum("total_amount"), Value(0, output_field=DecimalField()))
        )["total"] or Decimal("0")

        return {
            "total_monthly_revenue": billable_revenue,
            "total_monthly_expenses": total_expenses,
            "gross_margin": gross_margin,
            "margin_percentage": round(margin_percentage, 2),
            "outstanding_ar": outstanding_ar,
        }

    def _get_revenue_analysis(self, project_ids, month_start, last_month_start, last_month_end):
        """Analyze revenue trends and project/client breakdown."""
        # Current month revenue
        current_revenue = TimeEntry.objects.filter(
            project__in=project_ids, start_time__date__gte=month_start, is_billable=True
        ).aggregate(
            total=Coalesce(
                Sum(F("duration_hours") * F("hourly_rate")),
                Value(0, output_field=DecimalField()),
            )
        )[
            "total"
        ] or Decimal(
            "0"
        )

        # Last month revenue for comparison
        last_month_revenue = TimeEntry.objects.filter(
            project__in=project_ids,
            start_time__date__gte=last_month_start,
            start_time__date__lte=last_month_end,
            is_billable=True,
        ).aggregate(
            total=Coalesce(
                Sum(F("duration_hours") * F("hourly_rate")),
                Value(0, output_field=DecimalField()),
            )
        )[
            "total"
        ] or Decimal(
            "0"
        )

        # Revenue growth calculation
        revenue_growth = 0
        if last_month_revenue > 0:
            revenue_growth = (current_revenue - last_month_revenue) / last_month_revenue * 100

        # Revenue by project for drill-down
        revenue_by_project = (
            TimeEntry.objects.filter(
                project__in=project_ids,
                start_time__date__gte=month_start,
                is_billable=True,
            )
            .values("project__name", "project__client__name")
            .annotate(
                revenue=Coalesce(
                    Sum(F("duration_hours") * F("hourly_rate")),
                    Value(0, output_field=DecimalField()),
                ),
                hours=Coalesce(Sum("duration_hours"), Value(0, output_field=DecimalField())),
            )
            .order_by("-revenue")[:10]
        )

        return {
            "current_month_revenue": current_revenue,
            "last_month_revenue": last_month_revenue,
            "revenue_growth_percentage": round(revenue_growth, 2),
            "revenue_by_project": list(revenue_by_project),
        }

    def _get_enhanced_cost_analysis(self, project_ids, month_start, quarter_start):
        """Enhanced cost breakdown including labor and direct expenses."""
        # Labor costs breakdown
        labor_costs = TimeEntry.objects.filter(project__in=project_ids, start_time__date__gte=month_start).aggregate(
            billable_cost=Coalesce(
                Sum(
                    Case(
                        When(
                            is_billable=True,
                            then=F("duration_hours") * F("hourly_rate"),
                        )
                    )
                ),
                Value(0, output_field=DecimalField()),
            ),
            non_billable_cost=Coalesce(
                Sum(
                    Case(
                        When(
                            is_billable=False,
                            then=F("duration_hours") * F("hourly_rate"),
                        )
                    )
                ),
                Value(0, output_field=DecimalField()),
            ),
        )

        # Expense breakdown by category
        expense_by_category = (
            Expense.objects.filter(
                project__in=project_ids,
                expense_date__gte=month_start,
                approval_status="approved",
            )
            .values("category")
            .annotate(
                total=Coalesce(Sum("amount"), Value(0, output_field=DecimalField())),
                count=Count("id"),
            )
            .order_by("-total")
        )

        # Monthly expense trend (last 6 months)
        expense_trend = []
        for i in range(6):
            period_start = (month_start - timedelta(days=30 * i)).replace(day=1)
            period_end = (period_start + timedelta(days=32)).replace(day=1) - timedelta(days=1)

            expenses = Expense.objects.filter(
                project__in=project_ids,
                expense_date__gte=period_start,
                expense_date__lte=period_end,
                approval_status="approved",
            ).aggregate(total=Coalesce(Sum("amount"), Value(0, output_field=DecimalField())))["total"] or Decimal("0")

            expense_trend.append({"month": period_start.strftime("%Y-%m"), "amount": float(expenses)})

        expense_trend.reverse()

        return {
            "billable_labor_cost": labor_costs["billable_cost"] or Decimal("0"),
            "non_billable_labor_cost": labor_costs["non_billable_cost"] or Decimal("0"),
            "expense_by_category": list(expense_by_category),
            "expense_trend_data": expense_trend,
        }

    def _get_profitability_analysis(self, project_ids, month_start):
        """Calculate project profitability metrics."""
        project_profitability = []

        for project in Project.objects.filter(id__in=project_ids):
            # Revenue calculation
            revenue = TimeEntry.objects.filter(
                project=project, start_time__date__gte=month_start, is_billable=True
            ).aggregate(
                total=Coalesce(
                    Sum(F("duration_hours") * F("hourly_rate")),
                    Value(0, output_field=DecimalField()),
                )
            )[
                "total"
            ] or Decimal(
                "0"
            )

            # Cost calculation (labor + expenses)
            labor_cost = TimeEntry.objects.filter(project=project, start_time__date__gte=month_start).aggregate(
                total=Coalesce(
                    Sum(F("duration_hours") * F("hourly_rate")),
                    Value(0, output_field=DecimalField()),
                )
            )["total"] or Decimal("0")

            expense_cost = Expense.objects.filter(
                project=project,
                expense_date__gte=month_start,
                approval_status="approved",
            ).aggregate(total=Coalesce(Sum("amount"), Value(0, output_field=DecimalField())))["total"] or Decimal("0")

            total_cost = labor_cost + expense_cost
            profit = revenue - total_cost
            margin = (profit / revenue * 100) if revenue > 0 else 0

            project_profitability.append(
                {
                    "project_id": project.id,
                    "project_name": project.name,
                    "client_name": (
                        getattr(project.client, "name", "No Client") if hasattr(project, "client") else "No Client"
                    ),
                    "revenue": revenue,
                    "total_cost": total_cost,
                    "profit": profit,
                    "margin_percentage": round(margin, 2),
                    "billable_hours": TimeEntry.objects.filter(
                        project=project,
                        start_time__date__gte=month_start,
                        is_billable=True,
                    ).aggregate(Sum("duration_hours"))["duration_hours__sum"]
                    or 0,
                }
            )

        # Sort by profitability
        project_profitability.sort(key=lambda x: x["profit"], reverse=True)

        return {
            "project_profitability": project_profitability[:10],
        }

    def _get_budget_performance_analysis(self, project_ids):
        """Analyze budget vs actual performance."""
        budget_performance = (
            Budget.objects.filter(project__in=project_ids, status="active")
            .annotate(
                spent_amount=Coalesce(
                    Sum("budgetactual__actual_amount"),
                    Value(0, output_field=DecimalField()),
                )
            )
            .values("id", "project__name", "total_budget", "spent_amount")
            .annotate(
                remaining_budget=F("total_budget") - F("spent_amount"),
                utilization_percentage=Case(
                    When(
                        total_budget__gt=0,
                        then=F("spent_amount") * 100 / F("total_budget"),
                    ),
                    default=Value(0),
                    output_field=DecimalField(max_digits=5, decimal_places=2),
                ),
            )
        )

        # Budget alerts (over 80% utilized or overspent)
        budget_alerts = budget_performance.filter(utilization_percentage__gte=80)

        return {
            "budget_performance": list(budget_performance),
            "budget_alerts": list(budget_alerts),
        }

    def _get_ar_aging_analysis(self, project_ids):
        """Analyze accounts receivable aging."""
        from django.utils import timezone

        today = timezone.now().date()

        ar_aging = Invoice.objects.filter(project__in=project_ids, status__in=["sent", "overdue"]).aggregate(
            current=Coalesce(
                Sum(Case(When(due_date__gte=today, then="total_amount"))),
                Value(0, output_field=DecimalField()),
            ),
            overdue_1_30=Coalesce(
                Sum(
                    Case(
                        When(
                            due_date__lt=today,
                            due_date__gte=today - timedelta(days=30),
                            then="total_amount",
                        )
                    )
                ),
                Value(0, output_field=DecimalField()),
            ),
            overdue_31_60=Coalesce(
                Sum(
                    Case(
                        When(
                            due_date__lt=today - timedelta(days=30),
                            due_date__gte=today - timedelta(days=60),
                            then="total_amount",
                        )
                    )
                ),
                Value(0, output_field=DecimalField()),
            ),
            overdue_61_90=Coalesce(
                Sum(
                    Case(
                        When(
                            due_date__lt=today - timedelta(days=60),
                            due_date__gte=today - timedelta(days=90),
                            then="total_amount",
                        )
                    )
                ),
                Value(0, output_field=DecimalField()),
            ),
            overdue_90_plus=Coalesce(
                Sum(Case(When(due_date__lt=today - timedelta(days=90), then="total_amount"))),
                Value(0, output_field=DecimalField()),
            ),
        )

        return {
            "ar_aging": ar_aging,
        }

    def _get_utilization_metrics(self, project_ids, month_start, week_start):
        """Calculate team utilization and efficiency metrics."""
        # Team utilization rates
        team_utilization = (
            TimeEntry.objects.filter(project__in=project_ids, start_time__date__gte=month_start)
            .values("user__first_name", "user__last_name", "user__id")
            .annotate(
                total_hours=Coalesce(Sum("duration_hours"), Value(0, output_field=DecimalField())),
                billable_hours=Coalesce(
                    Sum(Case(When(is_billable=True, then="duration_hours"))),
                    Value(0, output_field=DecimalField()),
                ),
                revenue_generated=Coalesce(
                    Sum(
                        Case(
                            When(
                                is_billable=True,
                                then=F("duration_hours") * F("hourly_rate"),
                            )
                        )
                    ),
                    Value(0, output_field=DecimalField()),
                ),
            )
            .annotate(
                utilization_rate=Case(
                    When(
                        total_hours__gt=0,
                        then=F("billable_hours") * 100 / F("total_hours"),
                    ),
                    default=Value(0),
                    output_field=DecimalField(max_digits=5, decimal_places=2),
                )
            )
            .order_by("-utilization_rate")
        )

        return {
            "team_utilization": list(team_utilization),
        }

    def _get_chart_data_sets(self, project_ids, month_start, quarter_start):
        """Prepare Chart.js data sets for financial visualizations."""
        # Revenue vs Expense trend (last 6 months)
        trend_data = []
        for i in range(6):
            period_start = (month_start - timedelta(days=30 * i)).replace(day=1)
            period_end = (period_start + timedelta(days=32)).replace(day=1) - timedelta(days=1)

            revenue = TimeEntry.objects.filter(
                project__in=project_ids,
                start_time__date__gte=period_start,
                start_time__date__lte=period_end,
                is_billable=True,
            ).aggregate(
                total=Coalesce(
                    Sum(F("duration_hours") * F("hourly_rate")),
                    Value(0, output_field=DecimalField()),
                )
            )[
                "total"
            ] or Decimal(
                "0"
            )

            expenses = Expense.objects.filter(
                project__in=project_ids,
                expense_date__gte=period_start,
                expense_date__lte=period_end,
                approval_status="approved",
            ).aggregate(total=Coalesce(Sum("amount"), Value(0, output_field=DecimalField())))["total"] or Decimal("0")

            trend_data.append(
                {
                    "month": period_start.strftime("%Y-%m"),
                    "month_name": period_start.strftime("%b %Y"),
                    "revenue": float(revenue),
                    "expenses": float(expenses),
                    "profit": float(revenue - expenses),
                }
            )

        trend_data.reverse()  # Chronological order

        return {
            "financial_trend_chart_data": trend_data,
            "chart_labels": [item["month_name"] for item in trend_data],
            "chart_revenue_data": [item["revenue"] for item in trend_data],
            "chart_expense_data": [item["expenses"] for item in trend_data],
            "chart_profit_data": [item["profit"] for item in trend_data],
        }

    def _get_legacy_compatibility_context(self, user, start_of_week, start_of_month, project_ids):
        """Maintain compatibility with existing template structure."""
        today = timezone.now().date()

        # User's time entries for legacy calculations
        user_entries = TimeEntry.objects.filter(user=user, project__in=project_ids).select_related(
            "project", "work_type"
        )

        # Legacy time calculations
        today_hours = user_entries.filter(start_time__date=today).aggregate(total=Sum("duration_hours"))[
            "total"
        ] or Decimal("0.00")

        week_hours = user_entries.filter(start_time__date__gte=start_of_week).aggregate(total=Sum("duration_hours"))[
            "total"
        ] or Decimal("0.00")

        month_hours = user_entries.filter(start_time__date__gte=start_of_month).aggregate(total=Sum("duration_hours"))[
            "total"
        ] or Decimal("0.00")

        # Billable vs non-billable hours
        billable_hours = user_entries.filter(is_billable=True, start_time__date__gte=start_of_month).aggregate(
            total=Sum("duration_hours")
        )["total"] or Decimal("0.00")

        non_billable_hours = user_entries.filter(is_billable=False, start_time__date__gte=start_of_month).aggregate(
            total=Sum("duration_hours")
        )["total"] or Decimal("0.00")

        # Project statistics with enhanced data
        project_stats = (
            Project.objects.filter(id__in=project_ids)
            .annotate(
                total_hours=Coalesce(
                    Sum("financial_time_entries__duration_hours"),
                    Value(0, output_field=DecimalField()),
                ),
                billable_hours=Coalesce(
                    Sum(
                        "financial_time_entries__duration_hours",
                        filter=Q(financial_time_entries__is_billable=True),
                    ),
                    Value(0, output_field=DecimalField()),
                ),
                hourly_rate=Avg("financial_time_entries__hourly_rate"),
            )
            .order_by("-total_hours")[:10]
        )

        # Enhanced invoice statistics
        invoices = Invoice.objects.filter(project__in=project_ids)

        # Work type breakdown
        work_type_stats = (
            user_entries.filter(start_time__date__gte=start_of_month)
            .values("work_type__name")
            .annotate(
                hours=Sum("duration_hours"),
                rate=Avg("hourly_rate"),
                total_value=Sum(F("duration_hours") * F("hourly_rate")),
            )
            .order_by("-hours")[:10]
        )

        # Calculate billable percentage
        total_monthly_hours = billable_hours + non_billable_hours
        billable_percentage = (billable_hours / total_monthly_hours * 100) if total_monthly_hours > 0 else 0

        return {
            "today_hours": today_hours,
            "week_hours": week_hours,
            "month_hours": month_hours,
            "billable_hours": billable_hours,
            "non_billable_hours": non_billable_hours,
            "billable_percentage": round(billable_percentage, 2),
            "recent_entries": user_entries.order_by("-start_time")[:10],
            "project_stats": project_stats,
            "pending_invoices": invoices.filter(status="pending").count(),
            "paid_invoices": invoices.filter(status="paid").count(),
            "overdue_invoices": invoices.filter(status="overdue").count(),
            "total_invoiced": invoices.filter(status__in=["sent", "paid"]).aggregate(total=Sum("total_amount"))["total"]
            or Decimal("0.00"),
            "outstanding_amount": invoices.filter(status__in=["sent", "overdue"]).aggregate(total=Sum("total_amount"))[
                "total"
            ]
            or Decimal("0.00"),
            "work_type_stats": work_type_stats,
            "active_timer": TimeEntry.get_active_timer(user),
        }

    def _get_empty_dashboard_context(self):
        """Return empty context when no projects or insufficient permissions."""
        return {
            "today_hours": Decimal("0.00"),
            "week_hours": Decimal("0.00"),
            "month_hours": Decimal("0.00"),
            "billable_hours": Decimal("0.00"),
            "non_billable_hours": Decimal("0.00"),
            "billable_percentage": 0,
            "recent_entries": [],
            "project_stats": [],
            "pending_invoices": 0,
            "paid_invoices": 0,
            "overdue_invoices": 0,
            "total_invoiced": Decimal("0.00"),
            "outstanding_amount": Decimal("0.00"),
            "work_type_stats": [],
            "active_timer": None,
            "total_monthly_revenue": Decimal("0.00"),
            "total_monthly_expenses": Decimal("0.00"),
            "gross_margin": Decimal("0.00"),
            "margin_percentage": 0,
            "outstanding_ar": Decimal("0.00"),
            "project_profitability": [],
            "budget_performance": [],
            "ar_aging": {},
            "team_utilization": [],
            "financial_trend_chart_data": [],
        }


# ========== BUDGET MANAGEMENT VIEWS ==========


class BudgetListView(
    HTMXResponseMixin,
    LoginRequiredMixin,
    RoleRequiredMixin,
    ListView,
    OrganizationAccessMixin,
):
    """
    List view for budgets with project filtering.

    Shows budgets for projects the user has access to,
    with status filtering and pagination.
    """

    required_roles = ["stakeholder"]

    model = Budget
    template_name = "financial/budgets.html"
    context_object_name = "budgets"
    paginate_by = 25

    def get_queryset(self) -> QuerySet[Budget]:
        """Filter budgets by organization and user access."""
        queryset = (
            Budget.objects.filter(project__organization=self.request.user.organization)
            .select_related("project", "created_by")
            .order_by("-created_at")
        )

        # Filter by project if specified
        project_id = self.request.GET.get("project")
        if project_id:
            queryset = queryset.filter(project_id=project_id)

        # Filter by status if specified
        status = self.request.GET.get("status")
        if status:
            queryset = queryset.filter(status=status)

        return queryset

    def get_context_data(self, **kwargs: Any) -> dict[str, Any]:
        context = super().get_context_data(**kwargs)
        # Define status choices from the model field
        status_choices = [
            ("draft", _("Draft")),
            ("proposal", _("Proposal")),
            ("submitted", _("Submitted")),
            ("under_review", _("Under Review")),
            ("approved", _("Approved")),
            ("rejected", _("Rejected")),
            ("active", _("Active")),
            ("closed", _("Closed")),
        ]

        context.update(
            {
                "page_title": _("Budget Management"),
                "projects": Project.objects.filter(organization=self.request.user.organization),
                "status_choices": status_choices,
            }
        )
        return context


class BudgetDetailView(
    HTMXResponseMixin,
    LoginRequiredMixin,
    RoleRequiredMixin,
    DetailView,
    OrganizationAccessMixin,
):
    """
    Detail view for budgets.

    Shows detailed budget information with spending analysis
    and approval history.
    """

    required_roles = ["stakeholder"]

    model = Budget
    template_name = "financial/budget_detail.html"
    context_object_name = "budget"

    def get_queryset(self) -> QuerySet[Budget]:
        """Filter by organization access."""
        return Budget.objects.filter(project__organization=self.request.user.organization).select_related(
            "project", "created_by"
        )

    def get_context_data(self, **kwargs: Any) -> dict[str, Any]:
        context = super().get_context_data(**kwargs)
        budget = self.get_object()

        # Calculate spending analysis
        total_spent = TimeEntry.objects.filter(project=budget.project, is_billable=True).aggregate(
            total=Sum(F("duration_hours") * F("hourly_rate"))
        )["total"] or Decimal("0.00")

        remaining_budget = budget.total_budget - total_spent
        budget_utilization = (total_spent / budget.total_budget * 100) if budget.total_budget > 0 else 0

        context.update(
            {
                "page_title": f"Budget: {budget.name}",
                "total_spent": total_spent,
                "remaining_budget": remaining_budget,
                "budget_utilization": budget_utilization,
                "recent_expenses": TimeEntry.objects.filter(project=budget.project, is_billable=True).order_by(
                    "-start_time"
                )[:10],
            }
        )
        return context


class BudgetCreateView(
    HTMXResponseMixin,
    LoginRequiredMixin,
    RoleRequiredMixin,
    CreateView,
    OrganizationAccessMixin,
):
    """
    Create view for budgets.

    Allows users to create new budgets with proper organization
    association and validation.
    """

    required_roles = ["department-manager"]

    model = Budget
    template_name = "financial/budget_form.html"
    fields = [
        "project",
        "name",
        "description",
        "total_budget",
        "start_date",
        "end_date",
        "status",
    ]
    success_url = reverse_lazy("financial:budgets")

    def form_valid(self, form):
        """Set organization and user on budget creation."""
        form.instance.created_by = self.request.user
        return super().form_valid(form)

    def get_form(self, form_class=None):
        """Filter projects by organization."""
        form = super().get_form(form_class)
        form.fields["project"].queryset = Project.objects.filter(organization=self.request.user.organization)
        return form

    def get_context_data(self, **kwargs: Any) -> dict[str, Any]:
        context = super().get_context_data(**kwargs)
        context["page_title"] = _("Create Budget")
        return context


class BudgetUpdateView(
    HTMXResponseMixin,
    LoginRequiredMixin,
    RoleRequiredMixin,
    UpdateView,
    OrganizationAccessMixin,
):
    """
    Update view for budgets.

    Allows users to edit budgets with proper organization
    filtering and validation.
    """

    required_roles = ["department-manager"]

    model = Budget
    template_name = "financial/budget_form.html"
    fields = [
        "project",
        "name",
        "description",
        "total_budget",
        "start_date",
        "end_date",
        "status",
    ]
    success_url = reverse_lazy("financial:budgets")

    def get_queryset(self) -> QuerySet[Budget]:
        """Filter by organization access."""
        return Budget.objects.filter(project__organization=self.request.user.organization)

    def get_form(self, form_class=None):
        """Filter projects by organization."""
        form = super().get_form(form_class)
        form.fields["project"].queryset = Project.objects.filter(organization=self.request.user.organization)
        return form

    def get_context_data(self, **kwargs: Any) -> dict[str, Any]:
        context = super().get_context_data(**kwargs)
        context["page_title"] = f"Edit Budget: {self.get_object().name}"
        return context


@login_required
@require_http_methods(["POST"])
def approve_budget(request: HttpRequest, pk: str) -> HttpResponse:
    """
    Approve a budget.

    Updates budget status to approved and logs the action.
    Requires department manager role.
    """
    if not request.user.get_role_level(request.user.organization.id) <= 20:
        messages.error(request, _("You don't have permission to approve budgets."))
        return redirect("financial:budgets")

    try:
        budget = Budget.objects.get(pk=pk, project__organization=request.user.organization)

        budget.status = "approved"
        budget.approved_by = request.user
        budget.approved_at = timezone.now()
        budget.save()

        messages.success(request, f"Budget '{budget.name}' has been approved.")

        # Log the action
        # import logging
        # logger = logging.getLogger(__name__)
        # logger.info(
        #     f"Budget {budget.pk} approved by user {request.user.email} "
        #     f"in organization {request.user.organization.name}"
        # )

        return redirect("financial:budget_detail", pk=budget.pk)

    except Budget.DoesNotExist:
        messages.error(request, _("Budget not found."))
        return redirect("financial:budgets")
    except Exception:
        # import logging
        # logger = logging.getLogger(__name__)
        # logger.error(f"Error approving budget {pk}: {str(e)}")
        messages.error(request, _("Error approving budget. Please try again."))
        return redirect("financial:budgets")


@login_required
@require_http_methods(["POST"])
def reject_budget(request: HttpRequest, pk: str) -> HttpResponse:
    """
    Reject a budget.

    Updates budget status to rejected and logs the action.
    Requires department manager role.
    """
    if not request.user.get_role_level(request.user.organization.id) <= 20:
        messages.error(request, _("You don't have permission to reject budgets."))
        return redirect("financial:budgets")

    try:
        budget = Budget.objects.get(pk=pk, project__organization=request.user.organization)

        rejection_reason = request.POST.get("rejection_reason", "")

        budget.status = "rejected"
        budget.rejected_by = request.user
        budget.rejected_at = timezone.now()
        budget.rejection_reason = rejection_reason
        budget.save()

        messages.success(request, f"Budget '{budget.name}' has been rejected.")

        # Log the action
        # import logging
        # logger = logging.getLogger(__name__)
        # logger.info(
        #     f"Budget {budget.pk} rejected by user {request.user.email} "
        #     f"in organization {request.user.organization.name}. Reason: {rejection_reason}"
        # )

        return redirect("financial:budget_detail", pk=budget.pk)

    except Budget.DoesNotExist:
        messages.error(request, _("Budget not found."))
        return redirect("financial:budgets")
    except Exception:
        # import logging
        # logger = logging.getLogger(__name__)
        # logger.error(f"Error rejecting budget {pk}: {str(e)}")
        messages.error(request, _("Error rejecting budget. Please try again."))
        return redirect("financial:budgets")


# ========== FINANCIAL REPORTS ==========


@login_required
def time_summary_report(request: HttpRequest) -> HttpResponse:
    """
    Generate time summary report.

    Shows time tracking statistics by project, user, and work type
    with date range filtering.
    """
    # Get date range from request
    start_date = request.GET.get("start_date")
    end_date = request.GET.get("end_date")

    if not start_date:
        start_date = timezone.now().replace(day=1).date()  # First day of current month
    else:
        start_date = datetime.strptime(start_date, "%Y-%m-%d").date()

    if not end_date:
        end_date = timezone.now().date()
    else:
        end_date = datetime.strptime(end_date, "%Y-%m-%d").date()

    # Base queryset for user's organization
    base_queryset = TimeEntry.objects.filter(
        user__organization=request.user.organization,
        start_time__date__gte=start_date,
        start_time__date__lte=end_date,
    )

    # Project summary
    project_summary = (
        base_queryset.values("project__name")
        .annotate(
            total_hours=Sum("duration_hours"),
            billable_hours=Sum("duration_hours", filter=Q(is_billable=True)),
            total_cost=Sum(F("duration_hours") * F("hourly_rate")),
        )
        .order_by("-total_hours")
    )

    # User summary
    user_summary = (
        base_queryset.values("user__first_name", "user__last_name")
        .annotate(
            total_hours=Sum("duration_hours"),
            billable_hours=Sum("duration_hours", filter=Q(is_billable=True)),
            total_entries=Count("id"),
        )
        .order_by("-total_hours")
    )

    # Work type summary
    work_type_summary = (
        base_queryset.values("work_type__name")
        .annotate(
            total_hours=Sum("duration_hours"),
            avg_rate=Avg("hourly_rate"),
            total_cost=Sum(F("duration_hours") * F("hourly_rate")),
        )
        .order_by("-total_hours")
    )

    # Overall statistics
    total_stats = base_queryset.aggregate(
        total_hours=Sum("duration_hours"),
        billable_hours=Sum("duration_hours", filter=Q(is_billable=True)),
        total_entries=Count("id"),
        total_revenue=Sum(F("duration_hours") * F("hourly_rate"), filter=Q(is_billable=True)),
    )

    context = {
        "page_title": _("Time Summary Report"),
        "start_date": start_date,
        "end_date": end_date,
        "project_summary": project_summary,
        "user_summary": user_summary,
        "work_type_summary": work_type_summary,
        "total_stats": total_stats,
    }

    return render(request, "financial/reports/time_summary.html", context)


@login_required
def project_costs_report(request: HttpRequest) -> HttpResponse:
    """
    Generate project costs report.

    Shows detailed cost analysis by project with budget comparison
    and profitability analysis.
    """
    # Get date range from request
    start_date = request.GET.get("start_date")
    end_date = request.GET.get("end_date")

    if not start_date:
        start_date = timezone.now().replace(day=1).date()  # First day of current month
    else:
        start_date = datetime.strptime(start_date, "%Y-%m-%d").date()

    if not end_date:
        end_date = timezone.now().date()
    else:
        end_date = datetime.strptime(end_date, "%Y-%m-%d").date()

    # Project cost analysis
    projects = (
        Project.objects.filter(organization=request.user.organization)
        .annotate(
            total_hours=Sum(
                "financial_time_entries__duration_hours",
                filter=Q(
                    financial_time_entries__start_time__date__gte=start_date,
                    financial_time_entries__start_time__date__lte=end_date,
                ),
            ),
            billable_hours=Sum(
                "financial_time_entries__duration_hours",
                filter=Q(
                    financial_time_entries__start_time__date__gte=start_date,
                    financial_time_entries__start_time__date__lte=end_date,
                    financial_time_entries__is_billable=True,
                ),
            ),
            total_cost=Sum(
                F("financial_time_entries__duration_hours") * F("financial_time_entries__hourly_rate"),
                filter=Q(
                    financial_time_entries__start_time__date__gte=start_date,
                    financial_time_entries__start_time__date__lte=end_date,
                ),
            ),
            billable_revenue=Sum(
                F("financial_time_entries__duration_hours") * F("financial_time_entries__hourly_rate"),
                filter=Q(
                    financial_time_entries__start_time__date__gte=start_date,
                    financial_time_entries__start_time__date__lte=end_date,
                    financial_time_entries__is_billable=True,
                ),
            ),
        )
        .prefetch_related("budgets")
    )

    # Calculate profitability for each project
    project_data = []
    for project in projects:
        # Get latest budget
        latest_budget = project.budgets.filter(status="approved").order_by("-created_at").first()
        budget_amount = latest_budget.total_amount if latest_budget else Decimal("0.00")

        total_cost = project.total_cost or Decimal("0.00")
        billable_revenue = project.billable_revenue or Decimal("0.00")
        profit_margin = billable_revenue - total_cost
        profit_percentage = (profit_margin / billable_revenue * 100) if billable_revenue > 0 else 0

        project_data.append(
            {
                "project": project,
                "total_hours": project.total_hours or 0,
                "billable_hours": project.billable_hours or 0,
                "total_cost": total_cost,
                "billable_revenue": billable_revenue,
                "budget_amount": budget_amount,
                "budget_variance": budget_amount - total_cost,
                "profit_margin": profit_margin,
                "profit_percentage": profit_percentage,
            }
        )

    # Sort by total cost descending
    project_data.sort(key=lambda x: x["total_cost"], reverse=True)

    context = {
        "page_title": _("Project Costs Report"),
        "start_date": start_date,
        "end_date": end_date,
        "project_data": project_data,
    }

    return render(request, "financial/reports/project_costs.html", context)


@login_required
def revenue_analysis_report(request: HttpRequest) -> HttpResponse:
    """
    Generate revenue analysis report.

    Shows revenue trends, client analysis, and billing efficiency
    with monthly and quarterly breakdowns.
    """
    # Get date range from request
    start_date = request.GET.get("start_date")
    end_date = request.GET.get("end_date")

    if not start_date:
        start_date = (timezone.now() - timedelta(days=365)).date()  # Last year
    else:
        start_date = datetime.strptime(start_date, "%Y-%m-%d").date()

    if not end_date:
        end_date = timezone.now().date()
    else:
        end_date = datetime.strptime(end_date, "%Y-%m-%d").date()

    # Monthly revenue trends
    monthly_revenue = (
        TimeEntry.objects.filter(
            user__organization=request.user.organization,
            start_time__date__gte=start_date,
            start_time__date__lte=end_date,
            is_billable=True,
        )
        .extra(select={"month": "DATE_TRUNC('month', start_time)"})
        .values("month")
        .annotate(
            revenue=Sum(F("duration_hours") * F("hourly_rate")),
            hours=Sum("duration_hours"),
            avg_rate=Avg("hourly_rate"),
        )
        .order_by("month")
    )

    # Client analysis (by organization/project)
    client_analysis = (
        Project.objects.filter(organization=request.user.organization)
        .annotate(
            total_revenue=Sum(
                F("financial_time_entries__duration_hours") * F("financial_time_entries__hourly_rate"),
                filter=Q(
                    financial_time_entries__start_time__date__gte=start_date,
                    financial_time_entries__start_time__date__lte=end_date,
                    financial_time_entries__is_billable=True,
                ),
            ),
            total_hours=Sum(
                "financial_time_entries__duration_hours",
                filter=Q(
                    financial_time_entries__start_time__date__gte=start_date,
                    financial_time_entries__start_time__date__lte=end_date,
                    financial_time_entries__is_billable=True,
                ),
            ),
            avg_rate=Avg(
                "financial_time_entries__hourly_rate",
                filter=Q(
                    financial_time_entries__start_time__date__gte=start_date,
                    financial_time_entries__start_time__date__lte=end_date,
                    financial_time_entries__is_billable=True,
                ),
            ),
        )
        .filter(total_revenue__gt=0)
        .order_by("-total_revenue")
    )

    # Billing efficiency analysis
    billing_efficiency = TimeEntry.objects.filter(
        user__organization=request.user.organization,
        start_time__date__gte=start_date,
        start_time__date__lte=end_date,
    ).aggregate(
        total_hours=Sum("duration_hours"),
        billable_hours=Sum("duration_hours", filter=Q(is_billable=True)),
        total_revenue=Sum(F("duration_hours") * F("hourly_rate"), filter=Q(is_billable=True)),
        avg_billable_rate=Avg("hourly_rate", filter=Q(is_billable=True)),
    )

    # Calculate billable percentage
    total_hours = billing_efficiency["total_hours"] or 0
    billable_hours = billing_efficiency["billable_hours"] or 0
    billable_percentage = (billable_hours / total_hours * 100) if total_hours > 0 else 0

    context = {
        "page_title": _("Revenue Analysis Report"),
        "start_date": start_date,
        "end_date": end_date,
        "monthly_revenue": monthly_revenue,
        "client_analysis": client_analysis,
        "billing_efficiency": billing_efficiency,
        "billable_percentage": billable_percentage,
    }

    return render(request, "financial/reports/revenue_analysis.html", context)


@login_required
def export_financial_summary(request: HttpRequest) -> HttpResponse:
    """
    Export comprehensive financial summary.

    Generates Excel or CSV export of financial data including
    time entries, invoices, and budget information.
    """
    export_format = request.GET.get("format", "csv")
    start_date = request.GET.get("start_date")
    end_date = request.GET.get("end_date")

    if not start_date:
        start_date = timezone.now().replace(day=1).date()  # First day of current month
    else:
        start_date = datetime.strptime(start_date, "%Y-%m-%d").date()

    if not end_date:
        end_date = timezone.now().date()
    else:
        end_date = datetime.strptime(end_date, "%Y-%m-%d").date()

    try:
        # Get financial data
        time_entries = (
            TimeEntry.objects.filter(
                user__organization=request.user.organization,
                start_time__date__gte=start_date,
                start_time__date__lte=end_date,
            )
            .select_related("user", "project", "work_type")
            .order_by("-start_time")
        )

        if export_format == "csv":
            response = HttpResponse(content_type="text/csv")
            response["Content-Disposition"] = f'attachment; filename="financial_summary_{start_date}_{end_date}.csv"'

            writer = csv.writer(response)

            # Write header
            writer.writerow(
                [
                    "Date",
                    "User",
                    "Project",
                    "Work Type",
                    "Duration (Hours)",
                    "Hourly Rate",
                    "Total Cost",
                    "Billable",
                    "Description",
                ]
            )

            # Write data
            for entry in time_entries:
                writer.writerow(
                    [
                        entry.start_time.strftime("%Y-%m-%d"),
                        f"{entry.user.first_name} {entry.user.last_name}",
                        entry.project.name,
                        entry.work_type.name if entry.work_type else "",
                        entry.duration_hours,
                        entry.hourly_rate,
                        entry.duration_hours * entry.hourly_rate,
                        "Yes" if entry.is_billable else "No",
                        entry.description or "",
                    ]
                )

            # Log the export
            # import logging
            # logger = logging.getLogger(__name__)
            # logger.info(
            #     f"Financial summary CSV exported by user {request.user.email} "
            #     f"for period {start_date} to {end_date}"
            # )

            return response

        # JSON format
        data = {
            "export_date": timezone.now().isoformat(),
            "period": {
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat(),
            },
            "organization": request.user.organization.name,
            "time_entries": [
                {
                    "id": str(entry.id),
                    "date": entry.start_time.strftime("%Y-%m-%d"),
                    "user": f"{entry.user.first_name} {entry.user.last_name}",
                    "project": entry.project.name,
                    "work_type": entry.work_type.name if entry.work_type else None,
                    "duration_hours": float(entry.duration_hours),
                    "hourly_rate": float(entry.hourly_rate),
                    "total_cost": float(entry.duration_hours * entry.hourly_rate),
                    "is_billable": entry.is_billable,
                    "description": entry.description,
                }
                for entry in time_entries
            ],
        }

        response = JsonResponse(data)
        response["Content-Disposition"] = f'attachment; filename="financial_summary_{start_date}_{end_date}.json"'

        # Log the export
        # import logging
        # logger = logging.getLogger(__name__)
        # logger.info(
        #     f"Financial summary JSON exported by user {request.user.email} "
        #     f"for period {start_date} to {end_date}"
        # )

        return response

    except Exception:
        # import logging
        # logger = logging.getLogger(__name__)
        # logger.error(f"Error exporting financial summary: {str(e)}")
        messages.error(request, _("Error exporting financial data. Please try again."))
        return redirect("financial:reports")


@login_required
@require_http_methods(["POST"])
def approve_time_entry(request: HttpRequest, pk: str) -> HttpResponse:
    """
    Approve a time entry (HTMX endpoint).

    Args:
        request: HTTP request object
        pk: UUID of the time entry to approve

    Returns:
        HttpResponse: Redirect or HTMX response
    """
    time_entry = get_object_or_404(TimeEntry, pk=pk)

    # Check if user has permission to approve (project manager or department manager role)
    if not (
        request.user.is_staff
        or hasattr(request.user, "organization")
        and request.user.organization == time_entry.project.organization
    ):
        messages.error(request, _("You don't have permission to approve this time entry."))
        return redirect("financial:time_entries")

    try:
        # Update approval status
        time_entry.approval_status = "approved"
        time_entry.approved_by = request.user
        time_entry.approved_at = timezone.now()
        time_entry.save(update_fields=["approval_status", "approved_by", "approved_at"])

        messages.success(request, _("Time entry approved successfully."))

        # Return HTMX response if requested
        if request.headers.get("HX-Request"):
            return render(
                request,
                "financial/partials/time_entry_updated.html",
                {
                    "time_entry": time_entry,
                    "message": "Time entry approved successfully.",
                },
            )

    except Exception:
        messages.error(request, _("Error approving time entry. Please try again."))

    return redirect("financial:time_entries")


@login_required
@require_http_methods(["POST"])
def duplicate_time_entry(request: HttpRequest, pk: str) -> HttpResponse:
    """
    Duplicate a time entry (HTMX endpoint).

    Args:
        request: HTTP request object
        pk: UUID of the time entry to duplicate

    Returns:
        HttpResponse: Redirect or HTMX response
    """
    original_entry = get_object_or_404(TimeEntry, pk=pk, user=request.user)

    try:
        # Create duplicate with current date
        duplicate_entry = TimeEntry.objects.create(
            user=request.user,
            project=original_entry.project,
            task=original_entry.task,
            work_type=original_entry.work_type,
            description=original_entry.description,
            hourly_rate=original_entry.hourly_rate,
            start_time=timezone.now().replace(hour=9, minute=0, second=0, microsecond=0),  # Start at 9 AM today
            duration_hours=original_entry.duration_hours,
            is_billable=original_entry.is_billable,
            billing_status="pending",
            approval_status="pending",
        )

        # Calculate end time
        if duplicate_entry.start_time and duplicate_entry.duration_hours:
            duration_delta = timedelta(hours=float(duplicate_entry.duration_hours))
            duplicate_entry.end_time = duplicate_entry.start_time + duration_delta
            duplicate_entry.save(update_fields=["end_time"])

        messages.success(request, _("Time entry duplicated successfully."))

        # Return HTMX response if requested
        if request.headers.get("HX-Request"):
            return render(
                request,
                "financial/partials/time_entry_created.html",
                {
                    "time_entry": duplicate_entry,
                    "message": "Time entry duplicated successfully.",
                },
            )

    except Exception:
        messages.error(request, _("Error duplicating time entry. Please try again."))

    return redirect("financial:time_entries")


@login_required
@require_http_methods(["POST"])
def bulk_approve_entries(request: HttpRequest) -> HttpResponse:
    """
    Bulk approve multiple time entries (HTMX endpoint).

    Args:
        request: HTTP request object with entry_ids in POST data

    Returns:
        HttpResponse: Redirect or HTMX response
    """
    entry_ids = request.POST.getlist("entry_ids")

    if not entry_ids:
        messages.error(request, _("No time entries selected for approval."))
        return redirect("financial:time_entries")

    try:
        # Filter entries user can approve
        time_entries = TimeEntry.objects.filter(pk__in=entry_ids, project__organization=request.user.organization)

        # Bulk update
        updated_count = time_entries.update(
            approval_status="approved",
            approved_by=request.user,
            approved_at=timezone.now(),
        )

        messages.success(
            request,
            _("%(count)d time entries approved successfully.") % {"count": updated_count},
        )

        # Return HTMX response if requested
        if request.headers.get("HX-Request"):
            return render(
                request,
                "financial/partials/bulk_approval_success.html",
                {
                    "approved_count": updated_count,
                    "message": f"{updated_count} time entries approved successfully.",
                },
            )

    except Exception:
        messages.error(request, _("Error approving time entries. Please try again."))

    return redirect("financial:time_entries")


@login_required
@require_http_methods(["POST"])
def bulk_reject_entries(request: HttpRequest) -> HttpResponse:
    """
    Bulk reject multiple time entries (HTMX endpoint).

    Args:
        request: HTTP request object with entry_ids in POST data

    Returns:
        HttpResponse: Redirect or HTMX response
    """
    entry_ids = request.POST.getlist("entry_ids")

    if not entry_ids:
        messages.error(request, _("No time entries selected for rejection."))
        return redirect("financial:time_entries")

    try:
        # Filter entries user can reject
        time_entries = TimeEntry.objects.filter(pk__in=entry_ids, project__organization=request.user.organization)

        # Bulk update
        updated_count = time_entries.update(
            approval_status="rejected",
            approved_by=request.user,
            approved_at=timezone.now(),
        )

        messages.success(
            request,
            _("%(count)d time entries rejected successfully.") % {"count": updated_count},
        )

        # Return HTMX response if requested
        if request.headers.get("HX-Request"):
            return render(
                request,
                "financial/partials/bulk_rejection_success.html",
                {
                    "rejected_count": updated_count,
                    "message": f"{updated_count} time entries rejected successfully.",
                },
            )

    except Exception:
        messages.error(request, _("Error rejecting time entries. Please try again."))

    return redirect("financial:time_entries")


@login_required
@require_http_methods(["POST"])
def bulk_export_entries(request: HttpRequest) -> HttpResponse:
    """
    Bulk export multiple time entries to CSV (HTMX endpoint).

    Args:
        request: HTTP request object with entry_ids in POST data

    Returns:
        HttpResponse: CSV download or redirect
    """
    entry_ids = request.POST.getlist("entry_ids")

    if not entry_ids:
        messages.error(request, _("No time entries selected for export."))
        return redirect("financial:time_entries")

    try:
        # Filter entries user can export
        time_entries = (
            TimeEntry.objects.filter(pk__in=entry_ids, project__organization=request.user.organization)
            .select_related("user", "project", "work_type")
            .order_by("-start_time")
        )

        # Create CSV response
        response = HttpResponse(content_type="text/csv")
        response["Content-Disposition"] = (
            f'attachment; filename="time_entries_export_{timezone.now().strftime("%Y%m%d")}.csv"'
        )

        writer = csv.writer(response)
        writer.writerow(
            [
                "Date",
                "User",
                "Project",
                "Work Type",
                "Description",
                "Duration (Hours)",
                "Hourly Rate",
                "Total Cost",
                "Billable",
                "Billing Status",
                "Approval Status",
            ]
        )

        for entry in time_entries:
            writer.writerow(
                [
                    entry.start_time.strftime("%Y-%m-%d") if entry.start_time else "",
                    f"{entry.user.first_name} {entry.user.last_name}",
                    entry.project.name,
                    entry.work_type.name if entry.work_type else "",
                    entry.description or "",
                    str(entry.duration_hours) if entry.duration_hours else "0",
                    str(entry.hourly_rate) if entry.hourly_rate else "0",
                    (
                        str(entry.duration_hours * entry.hourly_rate)
                        if entry.duration_hours and entry.hourly_rate
                        else "0"
                    ),
                    "Yes" if entry.is_billable else "No",
                    entry.billing_status.title() if entry.billing_status else "Pending",
                    (entry.approval_status.title() if entry.approval_status else "Pending"),
                ]
            )

        return response

    except Exception:
        messages.error(request, _("Error exporting time entries. Please try again."))
        return redirect("financial:time_entries")


@login_required
@require_http_methods(["GET", "POST"])
def export_time_entries_csv(request: HttpRequest) -> HttpResponse:
    """
    Export time entries to CSV format.

    Args:
        request: HTTP request object with optional filters

    Returns:
        HttpResponse: CSV download
    """
    try:
        # Get filter parameters
        start_date = request.GET.get("start_date")
        end_date = request.GET.get("end_date")
        project_id = request.GET.get("project_id")
        user_id = request.GET.get("user_id")

        # Build queryset with organization filter
        time_entries = (
            TimeEntry.objects.filter(project__organization=request.user.organization)
            .select_related("user", "project", "work_type")
            .order_by("-start_time")
        )

        # Apply date filters
        if start_date:
            try:
                start_date_obj = datetime.strptime(start_date, "%Y-%m-%d").date()
                time_entries = time_entries.filter(start_time__date__gte=start_date_obj)
            except ValueError:
                pass

        if end_date:
            try:
                end_date_obj = datetime.strptime(end_date, "%Y-%m-%d").date()
                time_entries = time_entries.filter(start_time__date__lte=end_date_obj)
            except ValueError:
                pass

        # Apply project filter
        if project_id:
            time_entries = time_entries.filter(project_id=project_id)

        # Apply user filter (if staff or viewing own entries)
        if user_id:
            if request.user.is_staff or str(request.user.id) == user_id:
                time_entries = time_entries.filter(user_id=user_id)
        elif not request.user.is_staff:
            # Non-staff users can only see their own entries
            time_entries = time_entries.filter(user=request.user)

        # Create CSV response
        response = HttpResponse(content_type="text/csv")
        response["Content-Disposition"] = f'attachment; filename="time_entries_{timezone.now().strftime("%Y%m%d")}.csv"'

        writer = csv.writer(response)
        writer.writerow(
            [
                "Date",
                "User",
                "Email",
                "Project",
                "Task",
                "Work Type",
                "Start Time",
                "End Time",
                "Duration (Hours)",
                "Hourly Rate",
                "Total Cost",
                "Billable",
                "Description",
                "Billing Status",
                "Approval Status",
            ]
        )

        for entry in time_entries:
            writer.writerow(
                [
                    entry.start_time.strftime("%Y-%m-%d") if entry.start_time else "",
                    f"{entry.user.first_name} {entry.user.last_name}",
                    entry.user.email,
                    entry.project.name,
                    entry.task or "",
                    entry.work_type.name if entry.work_type else "",
                    (entry.start_time.strftime("%Y-%m-%d %H:%M:%S") if entry.start_time else ""),
                    (entry.end_time.strftime("%Y-%m-%d %H:%M:%S") if entry.end_time else ""),
                    str(entry.duration_hours) if entry.duration_hours else "0",
                    str(entry.hourly_rate) if entry.hourly_rate else "0",
                    (
                        str(entry.duration_hours * entry.hourly_rate)
                        if entry.duration_hours and entry.hourly_rate
                        else "0"
                    ),
                    "Yes" if entry.is_billable else "No",
                    entry.description or "",
                    entry.billing_status.title() if entry.billing_status else "Pending",
                    (entry.approval_status.title() if entry.approval_status else "Pending"),
                ]
            )

        return response

    except Exception:
        messages.error(request, _("Error exporting time entries. Please try again."))
        return redirect("financial:time_entries")


@login_required
@require_http_methods(["GET", "POST"])
def export_timesheet_entries_csv(request: HttpRequest) -> HttpResponse:
    """
    Export timesheet entries to CSV format.

    Args:
        request: HTTP request object with optional filters

    Returns:
        HttpResponse: CSV download
    """
    try:
        # Get filter parameters
        period_id = request.GET.get("period_id")
        start_date = request.GET.get("start_date")
        end_date = request.GET.get("end_date")

        # Base queryset with organization filter
        time_entries = (
            TimeEntry.objects.filter(project__organization=request.user.organization)
            .select_related("user", "project", "work_type")
            .order_by("-start_time")
        )

        # Apply timesheet period filter if specified
        if period_id:
            try:
                from .models import TimesheetPeriod

                period = TimesheetPeriod.objects.get(pk=period_id, organization=request.user.organization)
                time_entries = time_entries.filter(
                    start_time__date__gte=period.start_date,
                    start_time__date__lte=period.end_date,
                )
            except TimesheetPeriod.DoesNotExist:
                pass
        else:
            # Apply date filters
            if start_date:
                try:
                    start_date_obj = datetime.strptime(start_date, "%Y-%m-%d").date()
                    time_entries = time_entries.filter(start_time__date__gte=start_date_obj)
                except ValueError:
                    pass

            if end_date:
                try:
                    end_date_obj = datetime.strptime(end_date, "%Y-%m-%d").date()
                    time_entries = time_entries.filter(start_time__date__lte=end_date_obj)
                except ValueError:
                    pass

        # Filter by user if not staff
        if not request.user.is_staff:
            time_entries = time_entries.filter(user=request.user)

        # Create CSV response
        response = HttpResponse(content_type="text/csv")
        filename = f"timesheet_entries_{timezone.now().strftime('%Y%m%d')}.csv"
        response["Content-Disposition"] = f'attachment; filename="{filename}"'

        writer = csv.writer(response)
        writer.writerow(
            [
                "Week Starting",
                "User",
                "Email",
                "Project",
                "Total Hours",
                "Billable Hours",
                "Total Cost",
                "Status",
                "Submitted Date",
                "Approved Date",
            ]
        )

        # Group entries by user and week
        from collections import defaultdict

        weekly_summaries = defaultdict(
            lambda: {
                "total_hours": Decimal("0"),
                "billable_hours": Decimal("0"),
                "total_cost": Decimal("0"),
                "entries": [],
                "user": None,
                "projects": set(),
            }
        )

        for entry in time_entries:
            if entry.start_time:
                # Calculate week starting date (Monday)
                week_start = entry.start_time.date() - timedelta(days=entry.start_time.weekday())
                key = (entry.user.id, week_start)

                weekly_summaries[key]["user"] = entry.user
                weekly_summaries[key]["total_hours"] += entry.duration_hours or Decimal("0")
                if entry.is_billable:
                    weekly_summaries[key]["billable_hours"] += entry.duration_hours or Decimal("0")
                weekly_summaries[key]["total_cost"] += (entry.duration_hours or Decimal("0")) * (
                    entry.hourly_rate or Decimal("0")
                )
                weekly_summaries[key]["projects"].add(entry.project.name)
                weekly_summaries[key]["entries"].append(entry)

        # Write summary rows
        for (_user_id, week_start), summary in sorted(weekly_summaries.items()):
            user = summary["user"]
            projects_str = ", ".join(sorted(summary["projects"]))

            # Determine status based on entries
            submitted_entries = [e for e in summary["entries"] if e.approval_status != "pending"]
            approved_entries = [e for e in summary["entries"] if e.approval_status == "approved"]

            if len(approved_entries) == len(summary["entries"]):
                status = "Approved"
                approved_date = (
                    max(
                        [e.approved_at for e in approved_entries if e.approved_at],
                        default="",
                    ).strftime("%Y-%m-%d")
                    if approved_entries
                    else ""
                )
            elif submitted_entries:
                status = "Submitted"
                approved_date = ""
            else:
                status = "Draft"
                approved_date = ""

            submitted_date = min(
                [e.created_at for e in submitted_entries if hasattr(e, "created_at")],
                default="",
            )
            if submitted_date:
                submitted_date = submitted_date.strftime("%Y-%m-%d")
            else:
                submitted_date = ""

            writer.writerow(
                [
                    week_start.strftime("%Y-%m-%d"),
                    f"{user.first_name} {user.last_name}",
                    user.email,
                    projects_str,
                    str(summary["total_hours"]),
                    str(summary["billable_hours"]),
                    str(summary["total_cost"]),
                    status,
                    submitted_date,
                    approved_date,
                ]
            )

        return response

    except Exception:
        messages.error(request, _("Error exporting timesheet entries. Please try again."))
        return redirect("financial:timesheet_summary")


@login_required
@require_http_methods(["GET", "POST"])
def export_reports_csv(request: HttpRequest) -> HttpResponse:
    """
    Export financial reports data to CSV format.

    Args:
        request: HTTP request object with optional filters

    Returns:
        HttpResponse: CSV download
    """
    try:
        # Get filter parameters
        report_type = request.GET.get("report_type", "summary")
        start_date = request.GET.get("start_date")
        end_date = request.GET.get("end_date")
        project_id = request.GET.get("project_id")

        # Base queryset with organization filter
        time_entries = (
            TimeEntry.objects.filter(project__organization=request.user.organization)
            .select_related("user", "project", "work_type")
            .order_by("-start_time")
        )

        # Apply date filters
        if start_date:
            try:
                start_date_obj = datetime.strptime(start_date, "%Y-%m-%d").date()
                time_entries = time_entries.filter(start_time__date__gte=start_date_obj)
            except ValueError:
                pass

        if end_date:
            try:
                end_date_obj = datetime.strptime(end_date, "%Y-%m-%d").date()
                time_entries = time_entries.filter(start_time__date__lte=end_date_obj)
            except ValueError:
                pass

        # Apply project filter
        if project_id:
            time_entries = time_entries.filter(project_id=project_id)

        # Filter by user if not staff
        if not request.user.is_staff:
            time_entries = time_entries.filter(user=request.user)

        # Create CSV response
        response = HttpResponse(content_type="text/csv")
        filename = f"financial_reports_{report_type}_{timezone.now().strftime('%Y%m%d')}.csv"
        response["Content-Disposition"] = f'attachment; filename="{filename}"'

        writer = csv.writer(response)

        if report_type == "detailed":
            # Detailed report with all entries
            writer.writerow(
                [
                    "Date",
                    "User",
                    "Email",
                    "Project",
                    "Task",
                    "Work Type",
                    "Start Time",
                    "End Time",
                    "Duration (Hours)",
                    "Hourly Rate",
                    "Total Cost",
                    "Billable",
                    "Description",
                    "Billing Status",
                    "Approval Status",
                    "Approved By",
                    "Approved Date",
                ]
            )

            for entry in time_entries:
                writer.writerow(
                    [
                        (entry.start_time.strftime("%Y-%m-%d") if entry.start_time else ""),
                        f"{entry.user.first_name} {entry.user.last_name}",
                        entry.user.email,
                        entry.project.name,
                        entry.task or "",
                        entry.work_type.name if entry.work_type else "",
                        (entry.start_time.strftime("%Y-%m-%d %H:%M:%S") if entry.start_time else ""),
                        (entry.end_time.strftime("%Y-%m-%d %H:%M:%S") if entry.end_time else ""),
                        str(entry.duration_hours) if entry.duration_hours else "0",
                        str(entry.hourly_rate) if entry.hourly_rate else "0",
                        (
                            str(entry.duration_hours * entry.hourly_rate)
                            if entry.duration_hours and entry.hourly_rate
                            else "0"
                        ),
                        "Yes" if entry.is_billable else "No",
                        entry.description or "",
                        (entry.billing_status.title() if entry.billing_status else "Pending"),
                        (entry.approval_status.title() if entry.approval_status else "Pending"),
                        (
                            f"{entry.approved_by.first_name} {entry.approved_by.last_name}"
                            if hasattr(entry, "approved_by") and entry.approved_by
                            else ""
                        ),
                        (
                            entry.approved_at.strftime("%Y-%m-%d")
                            if hasattr(entry, "approved_at") and entry.approved_at
                            else ""
                        ),
                    ]
                )

        elif report_type == "project_summary":
            # Project summary report
            writer.writerow(
                [
                    "Project",
                    "Total Hours",
                    "Billable Hours",
                    "Total Cost",
                    "Average Hourly Rate",
                    "Number of Entries",
                    "Number of Users",
                ]
            )

            # Group by project
            from django.db.models import Avg

            project_summaries = (
                time_entries.values("project__name")
                .annotate(
                    total_hours=Sum("duration_hours"),
                    billable_hours=Sum("duration_hours", filter=Q(is_billable=True)),
                    total_cost=Sum(F("duration_hours") * F("hourly_rate")),
                    avg_rate=Avg("hourly_rate"),
                    entry_count=Count("id"),
                    user_count=Count("user", distinct=True),
                )
                .order_by("-total_hours")
            )

            for summary in project_summaries:
                writer.writerow(
                    [
                        summary["project__name"],
                        str(summary["total_hours"] or 0),
                        str(summary["billable_hours"] or 0),
                        str(summary["total_cost"] or 0),
                        str(summary["avg_rate"] or 0),
                        str(summary["entry_count"]),
                        str(summary["user_count"]),
                    ]
                )

        elif report_type == "user_summary":
            # User summary report
            writer.writerow(
                [
                    "User",
                    "Email",
                    "Total Hours",
                    "Billable Hours",
                    "Total Cost",
                    "Number of Projects",
                    "Number of Entries",
                    "Last Entry Date",
                ]
            )

            # Group by user
            user_summaries = (
                time_entries.values("user__first_name", "user__last_name", "user__email")
                .annotate(
                    total_hours=Sum("duration_hours"),
                    billable_hours=Sum("duration_hours", filter=Q(is_billable=True)),
                    total_cost=Sum(F("duration_hours") * F("hourly_rate")),
                    project_count=Count("project", distinct=True),
                    entry_count=Count("id"),
                    last_entry=Max("start_time"),
                )
                .order_by("-total_hours")
            )

            for summary in user_summaries:
                writer.writerow(
                    [
                        f"{summary['user__first_name']} {summary['user__last_name']}",
                        summary["user__email"],
                        str(summary["total_hours"] or 0),
                        str(summary["billable_hours"] or 0),
                        str(summary["total_cost"] or 0),
                        str(summary["project_count"]),
                        str(summary["entry_count"]),
                        (summary["last_entry"].strftime("%Y-%m-%d") if summary["last_entry"] else ""),
                    ]
                )

        else:
            # Default summary report
            writer.writerow(
                [
                    "Report Period",
                    "Total Hours",
                    "Billable Hours",
                    "Total Cost",
                    "Number of Projects",
                    "Number of Users",
                    "Number of Entries",
                ]
            )

            # Calculate totals
            totals = time_entries.aggregate(
                total_hours=Sum("duration_hours"),
                billable_hours=Sum("duration_hours", filter=Q(is_billable=True)),
                total_cost=Sum(F("duration_hours") * F("hourly_rate")),
                project_count=Count("project", distinct=True),
                user_count=Count("user", distinct=True),
                entry_count=Count("id"),
            )

            period_str = f"{start_date or 'All'} to {end_date or 'All'}"

            writer.writerow(
                [
                    period_str,
                    str(totals["total_hours"] or 0),
                    str(totals["billable_hours"] or 0),
                    str(totals["total_cost"] or 0),
                    str(totals["project_count"]),
                    str(totals["user_count"]),
                    str(totals["entry_count"]),
                ]
            )

        return response

    except Exception:
        messages.error(request, _("Error exporting reports. Please try again."))
        return redirect("financial:reports")


@login_required
@require_http_methods(["GET", "POST"])
def export_reports_json(request: HttpRequest) -> JsonResponse:
    """
    Export financial reports data to JSON format.

    Args:
        request: HTTP request object with optional filters

    Returns:
        JsonResponse: JSON data download
    """
    try:
        # Get filter parameters
        report_type = request.GET.get("report_type", "summary")
        start_date = request.GET.get("start_date")
        end_date = request.GET.get("end_date")
        project_id = request.GET.get("project_id")

        # Base queryset with organization filter
        time_entries = (
            TimeEntry.objects.filter(project__organization=request.user.organization)
            .select_related("user", "project", "work_type")
            .order_by("-start_time")
        )

        # Apply date filters
        if start_date:
            try:
                start_date_obj = datetime.strptime(start_date, "%Y-%m-%d").date()
                time_entries = time_entries.filter(start_time__date__gte=start_date_obj)
            except ValueError:
                pass

        if end_date:
            try:
                end_date_obj = datetime.strptime(end_date, "%Y-%m-%d").date()
                time_entries = time_entries.filter(start_time__date__lte=end_date_obj)
            except ValueError:
                pass

        # Apply project filter
        if project_id:
            time_entries = time_entries.filter(project_id=project_id)

        # Filter by user if not staff
        if not request.user.is_staff:
            time_entries = time_entries.filter(user=request.user)

        # Build response data
        response_data = {
            "export_date": timezone.now().isoformat(),
            "report_type": report_type,
            "filters": {
                "start_date": start_date,
                "end_date": end_date,
                "project_id": project_id,
            },
            "organization": request.user.organization.name,
            "data": [],
        }

        if report_type == "detailed":
            # Detailed report with all entries
            response_data["data"] = [
                {
                    "id": str(entry.id),
                    "date": (entry.start_time.strftime("%Y-%m-%d") if entry.start_time else None),
                    "user": {
                        "id": str(entry.user.id),
                        "name": f"{entry.user.first_name} {entry.user.last_name}",
                        "email": entry.user.email,
                    },
                    "project": {
                        "id": str(entry.project.id),
                        "name": entry.project.name,
                    },
                    "task": entry.task,
                    "work_type": {
                        "id": str(entry.work_type.id) if entry.work_type else None,
                        "name": entry.work_type.name if entry.work_type else None,
                    },
                    "start_time": (entry.start_time.isoformat() if entry.start_time else None),
                    "end_time": entry.end_time.isoformat() if entry.end_time else None,
                    "duration_hours": (float(entry.duration_hours) if entry.duration_hours else 0),
                    "hourly_rate": float(entry.hourly_rate) if entry.hourly_rate else 0,
                    "total_cost": (
                        float(entry.duration_hours * entry.hourly_rate)
                        if entry.duration_hours and entry.hourly_rate
                        else 0
                    ),
                    "is_billable": entry.is_billable,
                    "description": entry.description,
                    "billing_status": entry.billing_status,
                    "approval_status": entry.approval_status,
                    "approved_by": (
                        {
                            "id": (
                                str(entry.approved_by.id)
                                if hasattr(entry, "approved_by") and entry.approved_by
                                else None
                            ),
                            "name": (
                                f"{entry.approved_by.first_name} {entry.approved_by.last_name}"
                                if hasattr(entry, "approved_by") and entry.approved_by
                                else None
                            ),
                        }
                        if hasattr(entry, "approved_by") and entry.approved_by
                        else None
                    ),
                    "approved_at": (
                        entry.approved_at.isoformat() if hasattr(entry, "approved_at") and entry.approved_at else None
                    ),
                }
                for entry in time_entries
            ]

        elif report_type == "project_summary":
            # Project summary report
            from django.db.models import Avg

            project_summaries = (
                time_entries.values("project__id", "project__name")
                .annotate(
                    total_hours=Sum("duration_hours"),
                    billable_hours=Sum("duration_hours", filter=Q(is_billable=True)),
                    total_cost=Sum(F("duration_hours") * F("hourly_rate")),
                    avg_rate=Avg("hourly_rate"),
                    entry_count=Count("id"),
                    user_count=Count("user", distinct=True),
                )
                .order_by("-total_hours")
            )

            response_data["data"] = [
                {
                    "project": {
                        "id": str(summary["project__id"]),
                        "name": summary["project__name"],
                    },
                    "total_hours": float(summary["total_hours"] or 0),
                    "billable_hours": float(summary["billable_hours"] or 0),
                    "total_cost": float(summary["total_cost"] or 0),
                    "average_hourly_rate": float(summary["avg_rate"] or 0),
                    "entry_count": summary["entry_count"],
                    "user_count": summary["user_count"],
                }
                for summary in project_summaries
            ]

        elif report_type == "user_summary":
            # User summary report
            user_summaries = (
                time_entries.values("user__id", "user__first_name", "user__last_name", "user__email")
                .annotate(
                    total_hours=Sum("duration_hours"),
                    billable_hours=Sum("duration_hours", filter=Q(is_billable=True)),
                    total_cost=Sum(F("duration_hours") * F("hourly_rate")),
                    project_count=Count("project", distinct=True),
                    entry_count=Count("id"),
                    last_entry=Max("start_time"),
                )
                .order_by("-total_hours")
            )

            response_data["data"] = [
                {
                    "user": {
                        "id": str(summary["user__id"]),
                        "name": f"{summary['user__first_name']} {summary['user__last_name']}",
                        "email": summary["user__email"],
                    },
                    "total_hours": float(summary["total_hours"] or 0),
                    "billable_hours": float(summary["billable_hours"] or 0),
                    "total_cost": float(summary["total_cost"] or 0),
                    "project_count": summary["project_count"],
                    "entry_count": summary["entry_count"],
                    "last_entry_date": (summary["last_entry"].isoformat() if summary["last_entry"] else None),
                }
                for summary in user_summaries
            ]

        else:
            # Default summary report
            totals = time_entries.aggregate(
                total_hours=Sum("duration_hours"),
                billable_hours=Sum("duration_hours", filter=Q(is_billable=True)),
                total_cost=Sum(F("duration_hours") * F("hourly_rate")),
                project_count=Count("project", distinct=True),
                user_count=Count("user", distinct=True),
                entry_count=Count("id"),
            )

            response_data["data"] = {
                "period": {"start_date": start_date, "end_date": end_date},
                "totals": {
                    "total_hours": float(totals["total_hours"] or 0),
                    "billable_hours": float(totals["billable_hours"] or 0),
                    "total_cost": float(totals["total_cost"] or 0),
                    "project_count": totals["project_count"],
                    "user_count": totals["user_count"],
                    "entry_count": totals["entry_count"],
                },
            }

        # Create JSON response with download headers
        response = JsonResponse(response_data)
        filename = f"financial_reports_{report_type}_{timezone.now().strftime('%Y%m%d')}.json"
        response["Content-Disposition"] = f'attachment; filename="{filename}"'

        return response

    except Exception as e:
        return JsonResponse(
            {
                "error": "Error exporting reports. Please try again.",
                "details": str(e) if request.user.is_staff else "An error occurred.",
            },
            status=500,
        )


@login_required
@require_http_methods(["GET", "POST"])
def export_reports_pdf(request: HttpRequest) -> HttpResponse:
    """
    Export financial reports data to PDF format.

    Args:
        request: HTTP request object with optional filters

    Returns:
        HttpResponse: PDF download
    """
    try:
        # For now, return a simple PDF placeholder
        # In a production system, this would use a library like ReportLab or WeasyPrint
        from django.http import HttpResponse

        # Get filter parameters
        report_type = request.GET.get("report_type", "summary")
        start_date = request.GET.get("start_date")
        end_date = request.GET.get("end_date")
        project_id = request.GET.get("project_id")

        # Simple PDF content (placeholder)
        pdf_content = f"""PDF Financial Report

Report Type: {report_type.title()}
Organization: {request.user.organization.name}
Generated: {timezone.now().strftime("%Y-%m-%d %H:%M:%S")}
Generated by: {request.user.get_full_name()}

Filters Applied:
- Start Date: {start_date or "All dates"}
- End Date: {end_date or "All dates"}
- Project: {project_id or "All projects"}

Note: This is a placeholder PDF export. In production, this would generate
a properly formatted PDF report using libraries like ReportLab or WeasyPrint.

For the complete implementation, the following would be included:
- Formatted tables with time entry data
- Charts and graphs for visual analysis
- Professional styling and branding
- Page headers and footers
- Summary statistics
"""

        # Create PDF response
        response = HttpResponse(pdf_content, content_type="application/pdf")
        filename = f"financial_reports_{report_type}_{timezone.now().strftime('%Y%m%d')}.pdf"
        response["Content-Disposition"] = f'attachment; filename="{filename}"'

        return response

    except Exception:
        messages.error(request, _("Error exporting PDF report. Please try again."))
        return redirect("financial:reports")


@login_required
@require_http_methods(["POST"])
def resume_timer(request: HttpRequest, pk: str) -> HttpResponse:
    """
    Resume a paused timer for a specific time entry.

    Args:
        request: HTTP request object
        pk: UUID of the time entry to resume

    Returns:
        HttpResponse: Redirect or HTMX response
    """
    try:
        # Get the time entry to resume
        time_entry = get_object_or_404(TimeEntry, pk=pk, user=request.user)

        # Check if the entry can be resumed
        if time_entry.end_time is not None:
            messages.error(request, _("Cannot resume a completed time entry."))
            return redirect("financial:time_entries")

        # Stop any other active timers for this user
        TimeEntry.objects.filter(user=request.user, end_time__isnull=True).exclude(pk=pk).update(
            end_time=timezone.now(), is_timer_running=False
        )

        # Resume this timer
        time_entry.is_timer_running = True
        time_entry.timer_resumed_at = timezone.now()
        time_entry.save(update_fields=["is_timer_running", "timer_resumed_at"])

        messages.success(request, _("Timer resumed successfully."))

        # Return HTMX response if requested
        if request.headers.get("HX-Request"):
            return render(
                request,
                "financial/partials/timer_started.html",
                {"time_entry": time_entry, "message": "Timer resumed successfully."},
            )

    except Exception:
        messages.error(request, _("Error resuming timer. Please try again."))

    return redirect("financial:time_tracking")


@login_required
@require_http_methods(["POST"])
def pause_timer(request: HttpRequest, pk: str) -> HttpResponse:
    """
    Pause a running timer for a specific time entry.

    Args:
        request: HTTP request object
        pk: UUID of the time entry to pause

    Returns:
        HttpResponse: Redirect or HTMX response
    """
    try:
        # Get the active time entry
        time_entry = get_object_or_404(
            TimeEntry,
            pk=pk,
            user=request.user,
            end_time__isnull=True,
            is_timer_running=True,
        )

        # Pause the timer
        time_entry.is_timer_running = False
        time_entry.timer_paused_at = timezone.now()

        # Calculate and update duration so far
        if time_entry.start_time:
            current_duration = timezone.now() - time_entry.start_time
            time_entry.duration_hours = Decimal(str(current_duration.total_seconds() / 3600)).quantize(Decimal("0.01"))

        time_entry.save(update_fields=["is_timer_running", "timer_paused_at", "duration_hours"])

        messages.success(request, _("Timer paused successfully."))

        # Return HTMX response if requested
        if request.headers.get("HX-Request"):
            return render(
                request,
                "financial/partials/timer_stopped.html",
                {"time_entry": time_entry, "message": "Timer paused successfully."},
            )

    except Exception:
        messages.error(request, _("Error pausing timer. Please try again."))

    return redirect("financial:time_tracking")


@login_required
@require_http_methods(["POST"])
def delete_timer(request: HttpRequest, pk: str) -> HttpResponse:
    """
    Delete a time entry/timer.

    Args:
        request: HTTP request object
        pk: UUID of the time entry to delete

    Returns:
        HttpResponse: Redirect or HTMX response
    """
    try:
        # Get the time entry
        time_entry = get_object_or_404(TimeEntry, pk=pk, user=request.user)

        # Store entry info for message
        entry_description = time_entry.description or f"Time entry for {time_entry.project.name}"

        # Delete the entry
        time_entry.delete()

        messages.success(request, _("Time entry deleted successfully."))

        # Return HTMX response if requested
        if request.headers.get("HX-Request"):
            return render(
                request,
                "financial/partials/time_entry_deleted.html",
                {"message": f"'{entry_description}' deleted successfully."},
            )

    except Exception:
        messages.error(request, _("Error deleting time entry. Please try again."))

    return redirect("financial:time_entries")


@login_required
@require_http_methods(["GET"])
@vary_on_headers("HX-Request")
def export_layout_settings(request):
    """Export dashboard layout settings."""
    # Get user's saved dashboard settings from session or user preferences
    layout_settings = {
        "dashboard_layout": request.session.get("dashboard_layout", "default"),
        "widget_positions": request.session.get("widget_positions", {}),
        "visible_widgets": request.session.get(
            "visible_widgets",
            ["time_summary", "recent_entries", "project_stats", "timer_status"],
        ),
        "chart_preferences": request.session.get(
            "chart_preferences",
            {
                "time_chart_type": "bar",
                "project_chart_type": "pie",
                "date_range": "week",
            },
        ),
        "table_settings": request.session.get(
            "table_settings",
            {
                "entries_per_page": 25,
                "default_sort": "-start_time",
                "visible_columns": [
                    "start_time",
                    "project",
                    "work_type",
                    "duration_hours",
                ],
            },
        ),
    }

    if request.htmx:
        return JsonResponse({"status": "success", "layout_settings": layout_settings})

    # For non-HTMX requests, return JSON download
    response = JsonResponse(layout_settings, json_dumps_params={"indent": 2})
    response["Content-Disposition"] = (
        f'attachment; filename="dashboard_layout_{timezone.now().strftime("%Y%m%d")}.json"'
    )
    return response


@login_required
@require_http_methods(["POST"])
@vary_on_headers("HX-Request")
def reset_dashboard_layout(request):
    """Reset dashboard layout to default settings."""
    # Clear user's dashboard customizations
    dashboard_keys = [
        "dashboard_layout",
        "widget_positions",
        "visible_widgets",
        "chart_preferences",
        "table_settings",
    ]

    for key in dashboard_keys:
        if key in request.session:
            del request.session[key]

    request.session.modified = True

    if request.htmx:
        return JsonResponse({"status": "success", "message": "Dashboard layout reset to default"})

    messages.success(request, "Dashboard layout has been reset to default settings.")
    return redirect("financial:dashboard")


@login_required
@require_http_methods(["GET"])
@vary_on_headers("HX-Request")
def get_last_entry_data(request):
    """Get data from user's last time entry for quick entry form."""
    try:
        last_entry = (
            TimeEntry.objects.filter(user=request.user)
            .select_related("project", "work_type")
            .order_by("-start_time")
            .first()
        )

        if not last_entry:
            return JsonResponse({"status": "no_data"})

        data = {
            "status": "success",
            "project_id": str(last_entry.project.id) if last_entry.project else None,
            "work_type_id": (str(last_entry.work_type.id) if last_entry.work_type else None),
            "description": last_entry.description or "",
            "hourly_rate": (str(last_entry.hourly_rate) if last_entry.hourly_rate else None),
        }

        return JsonResponse(data)

    except Exception as e:
        return JsonResponse(
            {"status": "error", "message": f"Error retrieving last entry: {str(e)}"},
            status=500,
        )


@login_required
@require_http_methods(["GET"])
@vary_on_headers("HX-Request")
def get_active_timer_data(request):
    """Get active timer data for status display."""
    try:
        active_timer = (
            TimeEntry.objects.filter(user=request.user, end_time__isnull=True)
            .select_related("project", "work_type")
            .first()
        )

        if not active_timer:
            return JsonResponse({"status": "no_timer"})

        # Calculate elapsed time
        elapsed_seconds = (timezone.now() - active_timer.start_time).total_seconds()
        hours = int(elapsed_seconds // 3600)
        minutes = int((elapsed_seconds % 3600) // 60)
        seconds = int(elapsed_seconds % 60)

        data = {
            "status": "active",
            "timer_id": str(active_timer.id),
            "project_name": (active_timer.project.name if active_timer.project else "No Project"),
            "work_type_name": (active_timer.work_type.name if active_timer.work_type else "General"),
            "description": active_timer.description or "",
            "start_time": active_timer.start_time.isoformat(),
            "elapsed_time": f"{hours:02d}:{minutes:02d}:{seconds:02d}",
            "elapsed_seconds": int(elapsed_seconds),
        }

        return JsonResponse(data)

    except Exception as e:
        return JsonResponse(
            {"status": "error", "message": f"Error retrieving timer data: {str(e)}"},
            status=500,
        )


@login_required
@require_http_methods(["POST"])
@vary_on_headers("HX-Request")
def save_time_entry_draft(request):
    """Save time entry as draft for later completion."""
    form_data = {
        "project_id": request.POST.get("project"),
        "work_type_id": request.POST.get("work_type"),
        "description": request.POST.get("description", ""),
        "start_time": request.POST.get("start_time"),
        "end_time": request.POST.get("end_time"),
        "duration_hours": request.POST.get("duration_hours"),
        "hourly_rate": request.POST.get("hourly_rate"),
    }

    # Save draft to session
    request.session["time_entry_draft"] = form_data
    request.session.modified = True

    if request.htmx:
        return JsonResponse({"status": "success", "message": "Draft saved successfully"})

    messages.success(request, "Time entry draft saved.")
    return redirect("financial:add_time_entry")


@login_required
@require_http_methods(["POST"])
@vary_on_headers("HX-Request")
def auto_save_time_entry(request):
    """Auto-save time entry form data periodically."""
    form_data = {
        "project_id": request.POST.get("project"),
        "work_type_id": request.POST.get("work_type"),
        "description": request.POST.get("description", ""),
        "start_time": request.POST.get("start_time"),
        "end_time": request.POST.get("end_time"),
        "duration_hours": request.POST.get("duration_hours"),
        "hourly_rate": request.POST.get("hourly_rate"),
        "auto_saved_at": timezone.now().isoformat(),
    }

    # Auto-save to session with timestamp
    request.session["time_entry_auto_save"] = form_data
    request.session.modified = True

    return JsonResponse({"status": "auto_saved", "timestamp": form_data["auto_saved_at"]})


# ========== COMPREHENSIVE TIMESHEET MANAGEMENT ==========


@requires_organization_access
def comprehensive_timesheet_management(request: HttpRequest) -> HttpResponse:
    """
    Comprehensive timesheet management interface for time tracking, approval workflows,
    and billing management with HTMX support for dynamic interactions.

    This view provides:
    - Time entry management and editing
    - Timesheet approval workflows
    - Project-based time tracking and billing
    - Real-time timer functionality
    - Comprehensive reporting and analytics
    - Export capabilities (CSV, PDF, JSON)
    - Organization-based filtering and permissions
    """
    organization = getattr(request.user, "organization", None)
    if not organization:
        messages.error(request, _("Organization membership required for timesheet access"))
        return redirect("core:dashboard")

    # Get current date and week parameters
    today = timezone.now().date()
    current_week_start = today - timedelta(days=today.weekday())

    # Handle week navigation
    week_offset = int(request.GET.get("week_offset", 0))
    selected_week_start = current_week_start + timedelta(weeks=week_offset)
    selected_week_end = selected_week_start + timedelta(days=6)

    # Filter parameters
    user_filter = request.GET.get("user_filter")
    project_filter = request.GET.get("project_filter")
    status_filter = request.GET.get("status_filter", "all")

    # Base queryset for time entries with organization isolation
    time_entries_qs = (
        TimeEntry.objects.filter(
            project__organization=organization, start_time__date__range=[selected_week_start, selected_week_end]
        )
        .select_related("user", "project", "task", "work_type", "approved_by")
        .prefetch_related("project__organization")
    )

    # Apply user filtering (managers can see all users, others see only their own)
    if request.user.has_perm("financial.view_all_timesheets"):
        if user_filter:
            time_entries_qs = time_entries_qs.filter(user_id=user_filter)
    else:
        time_entries_qs = time_entries_qs.filter(user=request.user)

    # Apply project filtering
    if project_filter:
        time_entries_qs = time_entries_qs.filter(project_id=project_filter)

    # Apply status filtering
    if status_filter != "all":
        time_entries_qs = time_entries_qs.filter(status=status_filter)

    time_entries = time_entries_qs.order_by("start_time")

    # Get timesheet periods for the selected week
    timesheet_periods = TimesheetPeriod.objects.filter(
        organization=organization, start_date__lte=selected_week_end, end_date__gte=selected_week_start
    ).select_related("locked_by")

    # Get or create current timesheet period
    current_period, created = TimesheetPeriod.objects.get_or_create(
        organization=organization,
        start_date=selected_week_start,
        end_date=selected_week_end,
        defaults={"name": f"Week of {selected_week_start.strftime('%B %d, %Y')}", "status": "open"},
    )

    # Calculate timesheet statistics
    total_hours = time_entries.aggregate(total=models.Sum("duration_hours"))["total"] or Decimal("0.00")

    billable_hours = time_entries.filter(work_type__billable=True).aggregate(total=models.Sum("duration_hours"))[
        "total"
    ] or Decimal("0.00")

    total_cost = time_entries.aggregate(
        total=models.Sum(models.F("duration_hours") * models.F("hourly_rate"), output_field=models.DecimalField())
    )["total"] or Decimal("0.00")

    # Group entries by user for approval workflows
    from collections import defaultdict

    entries_by_user = defaultdict(list)
    user_summaries = defaultdict(
        lambda: {
            "total_hours": Decimal("0.00"),
            "billable_hours": Decimal("0.00"),
            "total_cost": Decimal("0.00"),
            "user": None,
            "status": "draft",
        }
    )

    for entry in time_entries:
        entries_by_user[entry.user].append(entry)
        user_summaries[entry.user]["user"] = entry.user
        user_summaries[entry.user]["total_hours"] += entry.duration_hours or Decimal("0.00")
        if entry.work_type and entry.work_type.billable:
            user_summaries[entry.user]["billable_hours"] += entry.duration_hours or Decimal("0.00")
        if entry.hourly_rate and entry.duration_hours:
            user_summaries[entry.user]["total_cost"] += entry.hourly_rate * entry.duration_hours

    # Get active timer for current user
    active_timer = None
    try:
        active_timer = TimeEntry.objects.filter(
            user=request.user, project__organization=organization, end_time__isnull=True, start_time__isnull=False
        ).first()
    except TimeEntry.DoesNotExist:
        pass

    # Get available projects and work types for dropdowns
    projects = Project.objects.filter(organization=organization, status__in=["active", "planning"]).select_related(
        "organization"
    )

    work_types = WorkType.objects.filter(organization=organization, is_active=True)

    # Get users for manager filtering
    users = []
    if request.user.has_perm("financial.view_all_timesheets"):
        users = User.objects.filter(organization=organization, is_active=True).order_by("first_name", "last_name")

    # Handle form submissions
    if request.method == "POST":
        action = request.POST.get("action")

        if action == "submit_timesheet":
            # Submit timesheet for approval
            timesheet_entry, created = TimesheetEntry.objects.get_or_create(
                timesheet_period=current_period,
                user=request.user,
                defaults={
                    "total_hours": user_summaries[request.user]["total_hours"],
                    "billable_hours": user_summaries[request.user]["billable_hours"],
                    "total_amount": user_summaries[request.user]["total_cost"],
                    "status": "submitted",
                    "submitted_at": timezone.now(),
                },
            )

            if not created:
                timesheet_entry.status = "submitted"
                timesheet_entry.submitted_at = timezone.now()
                timesheet_entry.total_hours = user_summaries[request.user]["total_hours"]
                timesheet_entry.billable_hours = user_summaries[request.user]["billable_hours"]
                timesheet_entry.total_amount = user_summaries[request.user]["total_cost"]
                timesheet_entry.save()

            messages.success(request, _("Timesheet submitted for approval"))

        elif action == "approve_timesheet" and request.user.has_perm("financial.approve_timesheets"):
            user_id = request.POST.get("user_id")
            if user_id:
                try:
                    timesheet_entry = TimesheetEntry.objects.get(timesheet_period=current_period, user_id=user_id)
                    timesheet_entry.status = "approved"
                    timesheet_entry.approved_by = request.user
                    timesheet_entry.approved_at = timezone.now()
                    timesheet_entry.approval_notes = request.POST.get("approval_notes", "")
                    timesheet_entry.save()

                    messages.success(request, _("Timesheet approved successfully"))
                except TimesheetEntry.DoesNotExist:
                    messages.error(request, _("Timesheet not found"))

        elif action == "reject_timesheet" and request.user.has_perm("financial.approve_timesheets"):
            user_id = request.POST.get("user_id")
            if user_id:
                try:
                    timesheet_entry = TimesheetEntry.objects.get(timesheet_period=current_period, user_id=user_id)
                    timesheet_entry.status = "rejected"
                    timesheet_entry.approved_by = request.user
                    timesheet_entry.approved_at = timezone.now()
                    timesheet_entry.approval_notes = request.POST.get("approval_notes", "")
                    timesheet_entry.save()

                    messages.success(request, _("Timesheet rejected"))
                except TimesheetEntry.DoesNotExist:
                    messages.error(request, _("Timesheet not found"))

        # Redirect to prevent duplicate submissions
        return redirect("financial:comprehensive_timesheets")

    # Get recent activity for dashboard
    recent_entries = (
        TimeEntry.objects.filter(user=request.user, project__organization=organization)
        .select_related("project", "work_type")
        .order_by("-created_at")[:10]
    )

    context = {
        "time_entries": time_entries,
        "entries_by_user": dict(entries_by_user),
        "user_summaries": dict(user_summaries),
        "timesheet_periods": timesheet_periods,
        "current_period": current_period,
        "selected_week_start": selected_week_start,
        "selected_week_end": selected_week_end,
        "week_offset": week_offset,
        "total_hours": total_hours,
        "billable_hours": billable_hours,
        "total_cost": total_cost,
        "active_timer": active_timer,
        "projects": projects,
        "work_types": work_types,
        "users": users,
        "recent_entries": recent_entries,
        "user_filter": user_filter,
        "project_filter": project_filter,
        "status_filter": status_filter,
        "can_approve": request.user.has_perm("financial.approve_timesheets"),
        "can_view_all": request.user.has_perm("financial.view_all_timesheets"),
        "organization": organization,
    }

    return render(request, "financial/comprehensive_timesheets.html", context)
