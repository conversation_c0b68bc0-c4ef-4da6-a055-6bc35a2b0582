"""Messaging and communication models for CLEAR platform."""

import logging
import re
import uuid
from typing import ClassV<PERSON>

from django.contrib.auth import get_user_model
from django.contrib.contenttypes.fields import GenericForeignKey
from django.contrib.contenttypes.models import ContentType
from django.contrib.gis.db import models
from django.db.models import <PERSON>, <PERSON>, IntegerField, Max, When
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

User = get_user_model()
logger = logging.getLogger(__name__)


class BulkOperationMixin:
    """Mixin for models that support bulk operations."""

    @classmethod
    def bulk_create_optimized(cls, objects, batch_size=1000):
        """Optimized bulk create with batch processing."""
        return cls.objects.bulk_create(objects, batch_size=batch_size)

    @classmethod
    def bulk_update_optimized(cls, objects, fields, batch_size=1000):
        """Optimized bulk update with batch processing."""
        return cls.objects.bulk_update(objects, fields, batch_size=batch_size)


class MessagingPerformanceManager(models.Manager):
    """High-performance manager for messaging operations."""

    def get_queryset(self):
        """Return optimized queryset with common joins."""
        return super().get_queryset().select_related("user")

    def with_read_status(self, user):
        """Include read status for a specific user."""
        return self.get_queryset().prefetch_related(
            models.Prefetch(
                "read_by",
                queryset=MessageRead.objects.filter(user=user),
                to_attr="user_read_status",
            ),
        )

    def with_reactions(self):
        """Include reactions with user details."""
        return self.get_queryset().prefetch_related("reactions__user")

    def recent_messages(self, limit: int = 50):
        """Get recent messages with optimized queries."""
        return self.get_queryset().order_by("-created_at")[:limit]


class ConversationManager(models.Manager):
    """Optimized manager for conversation operations."""

    def get_queryset(self):
        """Return queryset with common optimizations."""
        return super().get_queryset().select_related("project")

    def active(self):
        """Return only active conversations."""
        return self.get_queryset().filter(is_active=True)

    def with_participants(self):
        """Include participant information."""
        return self.get_queryset().prefetch_related("members__user").annotate(participant_count=Count("members"))

    def with_last_message(self):
        """Include last message information."""
        return (
            self.get_queryset()
            .prefetch_related("messages")
            .annotate(last_message_timestamp=Max("messages__created_at"))
        )

    def for_user(self, user):
        """Get conversations for a specific user."""
        return self.get_queryset().filter(members__user=user).distinct()


class NotificationManager(models.Manager):
    """Manager for notification operations."""

    def unread_for_user(self, user):
        """Get unread notifications for user."""
        return self.get_queryset().filter(recipient=user, is_read=False).order_by("-created_at")

    def by_priority(self):
        """Order notifications by priority."""
        return (
            self.get_queryset()
            .annotate(
                priority_order=Case(
                    When(priority="urgent", then=0),
                    When(priority="high", then=1),
                    When(priority="normal", then=2),
                    When(priority="low", then=3),
                    default=2,
                    output_field=IntegerField(),
                ),
            )
            .order_by("priority_order", "-created_at")
        )

    def bulk_mark_read(self, user, notification_ids=None):
        """Mark notifications as read in bulk for a user."""
        queryset = self.filter(recipient=user, is_read=False)
        if notification_ids:
            queryset = queryset.filter(id__in=notification_ids)

        updated_count = queryset.update(is_read=True, read_at=timezone.now())
        return updated_count

    def bulk_mark_unread(self, user, notification_ids=None):
        """Mark notifications as unread in bulk for a user."""
        queryset = self.filter(recipient=user, is_read=True)
        if notification_ids:
            queryset = queryset.filter(id__in=notification_ids)

        updated_count = queryset.update(is_read=False, read_at=None)
        return updated_count

    def grouped_by_type(self, user, limit_per_group=5):
        """Get notifications grouped by type for a user."""

        # Get all notification types for this user
        notification_types = self.filter(recipient=user).values_list("notification_type", flat=True).distinct()

        grouped_notifications = {}
        for notification_type in notification_types:
            notifications = self.filter(recipient=user, notification_type=notification_type).order_by("-created_at")[
                :limit_per_group
            ]
            if notifications:
                grouped_notifications[notification_type] = {
                    "notifications": list(notifications),
                    "total_count": self.filter(recipient=user, notification_type=notification_type).count(),
                    "unread_count": self.filter(
                        recipient=user,
                        notification_type=notification_type,
                        is_read=False,
                    ).count(),
                }

        return grouped_notifications

    def create_with_preferences(
        self,
        recipient,
        notification_type,
        title,
        message,
        sender=None,
        priority="normal",
        action_url="",
        action_label="",
        content_object=None,
        category="",
        tags=None,
        metadata=None,
        **kwargs,
    ):
        """Create notification respecting user preferences."""

        # Get or create user notification settings
        try:
            settings = recipient.notification_settings
        except (AttributeError, NotificationSettings.DoesNotExist) as e:
            # Create default settings if they don't exist
            logger.debug(f"Creating default notification settings for user {recipient.id}: {e}")
            settings = NotificationSettings.objects.create(user=recipient)

        # Check if user wants this type of notification
        if not settings.should_notify(notification_type):
            return None

        # Check quiet hours
        if settings.is_in_quiet_hours():
            # Schedule for later or skip based on priority
            if priority not in ["urgent", "high"]:
                return None

        # Create the notification
        notification = self.create(
            recipient=recipient,
            sender=sender,
            notification_type=notification_type,
            title=title,
            message=message,
            priority=priority,
            action_url=action_url,
            action_label=action_label,
            content_object=content_object,
            category=category,
            tags=tags or [],
            metadata=metadata or {},
            **kwargs,
        )

        return notification

    def get_grouped_notifications(self, user, days=7):
        """Get notifications grouped by similar events."""
        from datetime import timedelta

        from django.utils import timezone

        cutoff_date = timezone.now() - timedelta(days=days)

        # Group by content_type, notification_type, and sender
        grouped = {}
        notifications = (
            self.filter(recipient=user, created_at__gte=cutoff_date)
            .select_related("sender", "content_type")
            .order_by("-created_at")
        )

        for notification in notifications:
            # Create grouping key
            group_key = (
                notification.notification_type,
                notification.content_type_id if notification.content_type else None,
                notification.sender_id if notification.sender else None,
                notification.category,
            )

            if group_key not in grouped:
                grouped[group_key] = {
                    "group_id": f"{notification.notification_type}_{hash(group_key)}",
                    "notification_type": notification.notification_type,
                    "sender": notification.sender,
                    "category": notification.category,
                    "content_type": notification.content_type,
                    "notifications": [],
                    "count": 0,
                    "unread_count": 0,
                    "latest_created_at": notification.created_at,
                    "priority": notification.priority,
                }

            grouped[group_key]["notifications"].append(notification)
            grouped[group_key]["count"] += 1
            if not notification.is_read:
                grouped[group_key]["unread_count"] += 1

            # Update latest timestamp and highest priority
            if notification.created_at > grouped[group_key]["latest_created_at"]:
                grouped[group_key]["latest_created_at"] = notification.created_at

            # Update to highest priority in group
            priority_order = {"urgent": 0, "high": 1, "normal": 2, "low": 3}
            current_priority = priority_order.get(grouped[group_key]["priority"], 2)
            new_priority = priority_order.get(notification.priority, 2)
            if new_priority < current_priority:
                grouped[group_key]["priority"] = notification.priority

        # Sort groups by latest activity and priority
        sorted_groups = sorted(
            grouped.values(),
            key=lambda x: (
                priority_order.get(x["priority"], 2),
                -x["latest_created_at"].timestamp(),
            ),
        )

        return sorted_groups


class BaseMessagingModel(models.Model):
    """Base model for all messaging-related models."""

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    created_at = models.DateTimeField(_("created at"), auto_now_add=True)
    updated_at = models.DateTimeField(_("updated at"), auto_now=True)

    class Meta:
        abstract = True


class Conversation(BaseMessagingModel):
    """Chat conversation model supporting multiple types of communication."""

    CONVERSATION_TYPES = [
        ("direct", _("Direct Message")),
        ("group", _("Group Chat")),
        ("project", _("Project Channel")),
        ("channel", _("Public Channel")),
    ]

    title = models.CharField(_("title"), max_length=255, blank=True)
    description = models.TextField(_("description"), blank=True)
    conversation_type = models.CharField(
        _("conversation type"),
        max_length=20,
        choices=CONVERSATION_TYPES,
        default="group",
    )
    is_active = models.BooleanField(_("active"), default=True)
    last_message_at = models.DateTimeField(_("last message at"), null=True, blank=True)
    last_activity = models.DateTimeField(_("last activity"), auto_now=True)

    # Organization context through project
    project = models.ForeignKey(
        "projects.Project",
        on_delete=models.CASCADE,
        related_name="conversations",
        verbose_name=_("project"),
        null=True,
        blank=True,
    )

    # Participants (many-to-many through ConversationMember)
    participants = models.ManyToManyField(User, through="ConversationMember", related_name="conversations", blank=True)

    objects = ConversationManager()

    class Meta:
        app_label = "messaging"
        db_table: ClassVar = "messaging_conversation"
        verbose_name = _("conversation")
        verbose_name_plural = _("conversations")
        ordering = ["-last_message_at", "-created_at"]
        indexes = [
            models.Index(fields=["conversation_type", "is_active"]),
            models.Index(fields=["project", "is_active"]),
            models.Index(fields=["last_message_at"]),
            models.Index(fields=["last_activity"]),
        ]

    def __str__(self) -> str:
        if self.title:
            return self.title
        if self.conversation_type == "direct":
            users = list(self.participants.all()[:2])
            if len(users) == 2:
                return f"DM: {users[0].username} & {users[1].username}"
        return f"{self.get_conversation_type_display()} ({self.id})"

    def get_participant_count(self) -> int:
        """Get number of participants in conversation."""
        return self.members.count()

    def add_participant(self, user: User, is_admin: bool = False) -> "ConversationMember":
        """Add a user to the conversation."""
        member, created = ConversationMember.objects.get_or_create(
            conversation=self,
            user=user,
            defaults={"is_admin": is_admin},
        )
        return member

    def remove_participant(self, user: User) -> bool:
        """Remove a user from the conversation."""
        try:
            member = ConversationMember.objects.get(conversation=self, user=user)
            member.delete()
            return True
        except ConversationMember.DoesNotExist:
            return False

    def get_unread_count_for_user(self, user: User) -> int:
        """Get the number of unread messages for a specific user."""
        try:
            member = self.members.get(user=user)
            if member.last_read_at:
                return self.messages.filter(created_at__gt=member.last_read_at).exclude(user=user).count()
            else:
                # If never read, count all messages except user's own
                return self.messages.exclude(user=user).count()
        except ConversationMember.DoesNotExist:
            return 0

    def has_unread_messages_for_user(self, user: User) -> bool:
        """Check if user has unread messages in this conversation."""
        return self.get_unread_count_for_user(user) > 0


class ConversationMember(models.Model):
    """Through model for conversation participants with additional metadata."""

    conversation = models.ForeignKey(Conversation, on_delete=models.CASCADE, related_name="members")
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name="conversation_memberships")

    is_admin = models.BooleanField(_("is admin"), default=False)
    notifications_enabled = models.BooleanField(_("notifications enabled"), default=True)
    joined_at = models.DateTimeField(_("joined at"), auto_now_add=True)
    last_read_at = models.DateTimeField(_("last read at"), null=True, blank=True)

    class Meta:
        app_label = "messaging"
        db_table: ClassVar = "messaging_conversation_member"
        verbose_name = _("conversation member")
        verbose_name_plural = _("conversation members")
        unique_together = ["conversation", "user"]
        indexes = [
            models.Index(fields=["conversation", "user"]),
            models.Index(fields=["user", "joined_at"]),
        ]

    def __str__(self) -> str:
        return f"{self.user.username} in {self.conversation}"


class Message(BaseMessagingModel):
    """Simple message model for conversation with soft delete support."""

    sender = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name="sent_messages",
        verbose_name=_("sender"),
    )
    conversation = models.ForeignKey(
        Conversation,
        on_delete=models.CASCADE,
        related_name="simple_messages",
        verbose_name=_("conversation"),
    )
    content = models.TextField(_("content"))
    timestamp = models.DateTimeField(_("timestamp"), default=timezone.now, db_index=True)
    is_read = models.BooleanField(_("is read"), default=False)
    is_deleted = models.BooleanField(_("is deleted"), default=False)

    class Meta:
        app_label = "messaging"
        db_table: ClassVar = "messaging_message"
        verbose_name = _("message")
        verbose_name_plural = _("messages")
        ordering = ["-timestamp"]
        indexes = [
            models.Index(fields=["conversation", "timestamp"]),
            models.Index(fields=["sender", "timestamp"]),
            models.Index(fields=["conversation", "is_deleted"]),
            models.Index(fields=["is_read", "timestamp"]),
        ]

    def __str__(self) -> str:
        content_preview = self.content[:50] + "..." if len(self.content) > 50 else self.content
        return f"{self.sender.username}: {content_preview}"

    def soft_delete(self):
        """Soft delete the message."""
        self.is_deleted = True
        self.save(update_fields=["is_deleted"])

    def mark_as_read(self):
        """Mark the message as read."""
        if not self.is_read:
            self.is_read = True
            self.save(update_fields=["is_read"])


class ChatMessage(BaseMessagingModel):
    """Main chat message model with rich features."""

    MESSAGE_TYPES = [
        ("text", _("Text Message")),
        ("image", _("Image")),
        ("file", _("File")),
        ("system", _("System Message")),
        ("announcement", _("Announcement")),
    ]

    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name="chat_messages",
        verbose_name=_("user"),
    )
    conversation = models.ForeignKey(
        Conversation,
        on_delete=models.CASCADE,
        related_name="messages",
        verbose_name=_("conversation"),
    )
    content = models.TextField(_("content"))
    message_type = models.CharField(_("message type"), max_length=20, choices=MESSAGE_TYPES, default="text")

    # Threading support
    reply_to = models.ForeignKey(
        "self",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="replies",
        verbose_name=_("reply to"),
    )

    # Additional context
    project = models.ForeignKey(
        "projects.Project",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="project_messages",
        verbose_name=_("project"),
    )
    channel = models.CharField(_("channel"), max_length=100, blank=True)
    is_urgent = models.BooleanField(_("urgent"), default=False)
    timestamp = models.DateTimeField(_("timestamp"), default=timezone.now)

    # File attachments (JSON field for flexibility)
    attachments = models.JSONField(_("attachments"), default=list, blank=True)

    # Read tracking (many-to-many through MessageRead)
    read_by = models.ManyToManyField(User, through="MessageRead", related_name="read_messages", blank=True)

    objects = MessagingPerformanceManager()

    class Meta:
        app_label = "messaging"
        db_table: ClassVar = "messaging_chat_message"
        verbose_name = _("chat message")
        verbose_name_plural = _("chat messages")
        ordering = ["-timestamp", "-created_at"]
        indexes = [
            models.Index(fields=["conversation", "timestamp"]),
            models.Index(fields=["user", "created_at"]),
            models.Index(fields=["message_type", "timestamp"]),
            models.Index(fields=["is_urgent", "timestamp"]),
        ]

    def __str__(self) -> str:
        content_preview = self.content[:50] + "..." if len(self.content) > 50 else self.content
        return f"{self.user.username}: {content_preview}"

    def get_thread_messages(self):
        """Get all replies to this message."""
        return self.replies.all().order_by("created_at")

    def get_thread_count(self) -> int:
        """Get count of replies to this message."""
        return self.replies.count()

    def mark_read_by(self, user: User):
        """Mark this message as read by a user."""
        MessageRead.objects.get_or_create(message=self, user=user, defaults={"read_at": timezone.now()})

    def is_read_by(self, user: User) -> bool:
        """Check if message has been read by user."""
        return self.read_by.filter(id=user.id).exists()


class MessageRead(models.Model):
    """Track which users have read which messages."""

    message = models.ForeignKey(ChatMessage, on_delete=models.CASCADE, related_name="read_status")
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name="message_reads")
    read_at = models.DateTimeField(_("read at"), default=timezone.now)

    class Meta:
        app_label = "messaging"
        db_table: ClassVar = "messaging_message_read"
        verbose_name = _("message read")
        verbose_name_plural = _("message reads")
        unique_together = ["message", "user"]
        indexes = [
            models.Index(fields=["message", "user"]),
            models.Index(fields=["user", "read_at"]),
        ]

    def __str__(self) -> str:
        return f"{self.user.username} read message {self.message.id}"


class MessageReaction(BaseMessagingModel):
    """Emoji reactions to messages."""

    message = models.ForeignKey(
        ChatMessage,
        on_delete=models.CASCADE,
        related_name="reactions",
        verbose_name=_("message"),
    )
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name="message_reactions",
        verbose_name=_("user"),
    )
    emoji = models.CharField(_("emoji"), max_length=10)
    emoji_unicode = models.CharField(_("emoji unicode"), max_length=20, blank=True)

    class Meta:
        app_label = "messaging"
        db_table: ClassVar = "messaging_message_reaction"
        verbose_name = _("message reaction")
        verbose_name_plural = _("message reactions")
        unique_together = ["message", "user", "emoji"]
        indexes = [
            models.Index(fields=["message", "emoji"]),
            models.Index(fields=["user", "created_at"]),
        ]

    def __str__(self) -> str:
        return f"{self.user.username} reacted {self.emoji} to message {self.message.id}"


class MessageMention(BaseMessagingModel):
    """Track @mentions in messages."""

    message = models.ForeignKey(
        ChatMessage,
        on_delete=models.CASCADE,
        related_name="mentions",
        verbose_name=_("message"),
    )
    mentioned_user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name="mentions",
        verbose_name=_("mentioned user"),
    )
    mention_text = models.CharField(_("mention text"), max_length=100)
    start_position = models.PositiveIntegerField(_("start position"), default=0)
    end_position = models.PositiveIntegerField(_("end position"), default=0)

    class Meta:
        app_label = "messaging"
        db_table: ClassVar = "messaging_message_mention"
        verbose_name = _("message mention")
        verbose_name_plural = _("message mentions")
        unique_together = ["message", "mentioned_user"]
        indexes = [
            models.Index(fields=["mentioned_user", "created_at"]),
            models.Index(fields=["message", "mentioned_user"]),
        ]

    def __str__(self) -> str:
        return f"Mention of {self.mentioned_user.username} in message {self.message.id}"


class MessageThread(BaseMessagingModel):
    """Organize message replies into threads."""

    root_message = models.OneToOneField(
        ChatMessage,
        on_delete=models.CASCADE,
        related_name="thread",
        verbose_name=_("root message"),
    )
    title = models.CharField(_("title"), max_length=255, blank=True)
    participants = models.ManyToManyField(User, related_name="message_threads", blank=True)
    last_reply_at = models.DateTimeField(_("last reply at"), null=True, blank=True)

    class Meta:
        app_label = "messaging"
        db_table: ClassVar = "messaging_message_thread"
        verbose_name = _("message thread")
        verbose_name_plural = _("message threads")
        ordering = ["-last_reply_at", "-created_at"]
        indexes = [
            models.Index(fields=["root_message"]),
            models.Index(fields=["last_reply_at"]),
        ]

    def __str__(self) -> str:
        return self.title or f"Thread for message {self.root_message.id}"

    def get_reply_count(self) -> int:
        """Get number of replies in this thread."""
        return self.root_message.replies.count()

    def add_participant(self, user: User):
        """Add a user to the thread participants."""
        self.participants.add(user)

    def update_last_reply(self):
        """Update the last reply timestamp."""
        self.last_reply_at = timezone.now()
        self.save(update_fields=["last_reply_at"])


class WhisperMessage(BaseMessagingModel):
    """Private direct messages between users."""

    sender = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name="sent_whispers",
        verbose_name=_("sender"),
    )
    recipient = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name="received_whispers",
        verbose_name=_("recipient"),
    )
    message = models.TextField(_("message"))
    is_read = models.BooleanField(_("read"), default=False)
    read_at = models.DateTimeField(_("read at"), null=True, blank=True)

    class Meta:
        app_label = "messaging"
        db_table: ClassVar = "messaging_whisper_message"
        verbose_name = _("whisper message")
        verbose_name_plural = _("whisper messages")
        ordering = ["-created_at"]
        indexes = [
            models.Index(fields=["recipient", "is_read"]),
            models.Index(fields=["sender", "created_at"]),
            models.Index(fields=["recipient", "created_at"]),
        ]

    def __str__(self) -> str:
        return f"Whisper from {self.sender.username} to {self.recipient.username}"

    def mark_read(self):
        """Mark whisper as read."""
        if not self.is_read:
            self.is_read = True
            self.read_at = timezone.now()
            self.save(update_fields=["is_read", "read_at"])


class Comment(BaseMessagingModel):
    """Universal commenting system for any model."""

    # Generic foreign key to any model
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE, related_name="messaging_comments")
    object_id = models.PositiveIntegerField()
    content_object = GenericForeignKey("content_type", "object_id")

    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name="messaging_comments",
        verbose_name=_("user"),
    )
    content = models.TextField(_("content"))

    # Threading support
    parent = models.ForeignKey(
        "self",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="replies",
        verbose_name=_("parent comment"),
    )

    # Soft deletion
    deleted_at = models.DateTimeField(_("deleted at"), null=True, blank=True)

    # Moderation fields
    is_hidden = models.BooleanField(_("is hidden"), default=False)
    hidden_at = models.DateTimeField(_("hidden at"), null=True, blank=True)
    flag_count = models.PositiveIntegerField(_("flag count"), default=0)
    auto_hidden_threshold = models.PositiveIntegerField(_("auto hidden threshold"), default=3)

    # Mentions in comments
    mentioned_users = models.ManyToManyField(User, related_name="messaging_comment_mentions", blank=True)

    class Meta:
        app_label = "messaging"
        db_table: ClassVar = "messaging_comment"
        verbose_name = _("comment")
        verbose_name_plural = _("comments")
        ordering = ["-created_at"]
        indexes = [
            models.Index(fields=["content_type", "object_id"]),
            models.Index(fields=["user", "created_at"]),
            models.Index(fields=["parent", "created_at"]),
            models.Index(fields=["deleted_at"]),
        ]

    def __str__(self) -> str:
        content_preview = self.content[:50] + "..." if len(self.content) > 50 else self.content
        return f"Comment by {self.user.username}: {content_preview}"

    def is_deleted(self) -> bool:
        """Check if comment is soft deleted."""
        return self.deleted_at is not None

    def soft_delete(self):
        """Soft delete the comment."""
        self.deleted_at = timezone.now()
        self.save(update_fields=["deleted_at"])

    def get_reply_count(self) -> int:
        """Get number of replies to this comment."""
        return self.replies.filter(deleted_at__isnull=True).count()

    def process_mentions(self):
        """Process @mentions in comment content."""
        mention_pattern = r"@(\w+)"
        mentions = re.findall(mention_pattern, self.content)

        for username in mentions:
            try:
                user = User.objects.get(username=username)
                self.mentioned_users.add(user)
            except User.DoesNotExist:
                continue

    def update_flag_count(self):
        """Update flag count and check for auto-hiding."""
        self.flag_count = self.flags.count()

        # Check if comment should be auto-hidden
        if self.flag_count >= self.auto_hidden_threshold and not self.is_hidden:
            self.hide_comment()
        else:
            self.save(update_fields=["flag_count"])

    def hide_comment(self):
        """Hide this comment due to moderation."""
        self.is_hidden = True
        self.hidden_at = timezone.now()
        self.save(update_fields=["is_hidden", "hidden_at", "flag_count"])

    def unhide_comment(self):
        """Unhide this comment after moderation review."""
        self.is_hidden = False
        self.hidden_at = None
        self.save(update_fields=["is_hidden", "hidden_at"])

    def get_flag_summary(self) -> dict:
        """Get summary of flags on this comment."""
        if not hasattr(self, "_flag_summary"):
            flags = self.flags.filter(is_resolved=False)
            summary = {}
            for flag in flags:
                reason = flag.get_reason_display()
                if reason not in summary:
                    summary[reason] = 0
                summary[reason] += 1
            self._flag_summary = summary
        return self._flag_summary

    def is_flagged(self) -> bool:
        """Check if comment has unresolved flags."""
        return self.flags.filter(is_resolved=False).exists()


class Notification(BaseMessagingModel):
    """Enhanced notification system with priority and scheduling."""

    NOTIFICATION_TYPES = [
        ("message", _("Message")),
        ("mention", _("Mention")),
        ("comment", _("Comment")),
        ("system", _("System")),
        ("task", _("Task")),
        ("project", _("Project")),
        ("document", _("Document")),
    ]

    PRIORITY_LEVELS = [
        ("low", _("Low")),
        ("normal", _("Normal")),
        ("high", _("High")),
        ("urgent", _("Urgent")),
    ]

    recipient = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name="messaging_notifications",
        verbose_name=_("recipient"),
    )
    sender = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="sent_notifications",
        verbose_name=_("sender"),
    )

    notification_type = models.CharField(
        _("notification type"),
        max_length=20,
        choices=NOTIFICATION_TYPES,
        default="system",
    )
    title = models.CharField(_("title"), max_length=255)
    message = models.TextField(_("message"))
    priority = models.CharField(_("priority"), max_length=10, choices=PRIORITY_LEVELS, default="normal")

    # Action link
    action_url = models.URLField(_("action URL"), blank=True)
    action_label = models.CharField(_("action label"), max_length=100, blank=True)

    # Status tracking
    is_read = models.BooleanField(_("read"), default=False)
    is_delivered = models.BooleanField(_("delivered"), default=False)
    read_at = models.DateTimeField(_("read at"), null=True, blank=True)
    delivered_at = models.DateTimeField(_("delivered at"), null=True, blank=True)

    # Scheduling
    scheduled_for = models.DateTimeField(_("scheduled for"), null=True, blank=True)
    expires_at = models.DateTimeField(_("expires at"), null=True, blank=True)

    # Generic relation to any model
    content_type = models.ForeignKey(
        ContentType,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="messaging_notifications",
    )
    object_id = models.PositiveIntegerField(null=True, blank=True)
    content_object = GenericForeignKey("content_type", "object_id")

    # Additional metadata
    category = models.CharField(_("category"), max_length=50, blank=True)
    tags = models.JSONField(_("tags"), default=list, blank=True)
    metadata = models.JSONField(_("metadata"), default=dict, blank=True)

    # Grouping support
    group = models.ForeignKey(
        "NotificationGroup",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="notifications",
        verbose_name=_("notification group"),
    )

    objects = NotificationManager()

    class Meta:
        app_label = "messaging"
        db_table: ClassVar = "messaging_notification"
        verbose_name = _("notification")
        verbose_name_plural = _("notifications")
        ordering = ["-created_at"]
        indexes = [
            models.Index(fields=["recipient", "is_read"]),
            models.Index(fields=["recipient", "priority", "created_at"]),
            models.Index(fields=["notification_type", "created_at"]),
            models.Index(fields=["scheduled_for"]),
            models.Index(fields=["expires_at"]),
        ]

    def __str__(self) -> str:
        return f"Notification for {self.recipient.username}: {self.title}"

    def mark_read(self):
        """Mark notification as read."""
        if not self.is_read:
            self.is_read = True
            self.read_at = timezone.now()
            self.save(update_fields=["is_read", "read_at"])

    def mark_delivered(self):
        """Mark notification as delivered."""
        if not self.is_delivered:
            self.is_delivered = True
            self.delivered_at = timezone.now()
            self.save(update_fields=["is_delivered", "delivered_at"])

    def is_expired(self) -> bool:
        """Check if notification has expired."""
        return self.expires_at and timezone.now() > self.expires_at

    def should_send_now(self) -> bool:
        """Check if notification should be sent now."""
        if not self.scheduled_for:
            return True
        return timezone.now() >= self.scheduled_for

    @classmethod
    def create_notification(
        cls,
        recipient,
        notification_type,
        title,
        message,
        sender=None,
        priority="normal",
        action_url="",
        action_label="",
        content_object=None,
        category="",
        tags=None,
        metadata=None,
        respect_preferences=True,
        **kwargs,
    ):
        """
        Create a notification with optional preference checking.

        Args:
            recipient: User to receive the notification
            notification_type: Type of notification (message, mention, etc.)
            title: Notification title
            message: Notification message content
            sender: User sending the notification (optional)
            priority: Priority level (low, normal, high, urgent)
            action_url: URL for notification action (optional)
            action_label: Label for action button (optional)
            content_object: Related object (optional)
            category: Notification category (optional)
            tags: List of tags (optional)
            metadata: Additional metadata dict (optional)
            respect_preferences: Whether to check user preferences (default True)
            **kwargs: Additional fields for the notification

        Returns:
            Notification instance or None if blocked by preferences
        """
        if respect_preferences:
            return cls.objects.create_with_preferences(
                recipient=recipient,
                notification_type=notification_type,
                title=title,
                message=message,
                sender=sender,
                priority=priority,
                action_url=action_url,
                action_label=action_label,
                content_object=content_object,
                category=category,
                tags=tags,
                metadata=metadata,
                **kwargs,
            )
        else:
            return cls.objects.create(
                recipient=recipient,
                sender=sender,
                notification_type=notification_type,
                title=title,
                message=message,
                priority=priority,
                action_url=action_url,
                action_label=action_label,
                content_object=content_object,
                category=category,
                tags=tags or [],
                metadata=metadata or {},
                **kwargs,
            )

    def get_display_title(self):
        """Get formatted title for display."""
        if self.sender and self.notification_type in ["message", "mention", "comment"]:
            return f"{self.sender.get_full_name() or self.sender.email}: {self.title}"
        return self.title

    def get_time_display(self):
        """Get human-readable time display."""
        from datetime import timedelta

        from django.utils import timezone

        now = timezone.now()
        diff = now - self.created_at

        if diff < timedelta(minutes=1):
            return "Just now"
        elif diff < timedelta(hours=1):
            minutes = int(diff.total_seconds() / 60)
            return f"{minutes}m ago"
        elif diff < timedelta(days=1):
            hours = int(diff.total_seconds() / 3600)
            return f"{hours}h ago"
        elif diff < timedelta(days=7):
            days = diff.days
            return f"{days}d ago"
        else:
            return self.created_at.strftime("%b %d")

    def get_related_notifications(self, limit=5):
        """Get related notifications for grouping."""
        return (
            Notification.objects.filter(
                recipient=self.recipient,
                notification_type=self.notification_type,
                content_type=self.content_type,
                sender=self.sender,
            )
            .exclude(id=self.id)
            .order_by("-created_at")[:limit]
        )


class NotificationGroup(BaseMessagingModel):
    """Group similar notifications together for better UX."""

    GROUP_TYPES = [
        ("similar_events", _("Similar Events")),
        ("bulk_action", _("Bulk Action")),
        ("digest", _("Digest")),
        ("related_content", _("Related Content")),
    ]

    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name="notification_groups",
        verbose_name=_("user"),
    )
    group_type = models.CharField(_("group type"), max_length=20, choices=GROUP_TYPES, default="similar_events")
    title = models.CharField(_("group title"), max_length=255)
    description = models.TextField(_("description"), blank=True)

    # Grouping criteria
    notification_type = models.CharField(_("notification type"), max_length=20, blank=True)
    category = models.CharField(_("category"), max_length=50, blank=True)
    content_type = models.ForeignKey(
        ContentType,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name=_("content type"),
    )

    # Aggregated data
    total_notifications = models.PositiveIntegerField(_("total notifications"), default=0)
    unread_notifications = models.PositiveIntegerField(_("unread notifications"), default=0)
    last_notification_at = models.DateTimeField(_("last notification at"), null=True, blank=True)

    # Group status
    is_read = models.BooleanField(_("is read"), default=False)
    is_collapsed = models.BooleanField(_("is collapsed"), default=True)

    class Meta:
        app_label = "messaging"
        db_table: ClassVar = "messaging_notification_group"
        verbose_name = _("notification group")
        verbose_name_plural = _("notification groups")
        ordering = ["-last_notification_at", "-created_at"]
        indexes = [
            models.Index(fields=["user", "is_read"]),
            models.Index(fields=["user", "notification_type"]),
            models.Index(fields=["content_type", "category"]),
            models.Index(fields=["last_notification_at"]),
        ]

    def __str__(self) -> str:
        return f"{self.title} ({self.total_notifications} notifications)"

    def update_counts(self):
        """Update aggregated counts from related notifications."""
        notifications = self.notifications.all()
        self.total_notifications = notifications.count()
        self.unread_notifications = notifications.filter(is_read=False).count()
        latest = notifications.order_by("-created_at").first()
        if latest:
            self.last_notification_at = latest.created_at
        self.is_read = self.unread_notifications == 0
        self.save(
            update_fields=[
                "total_notifications",
                "unread_notifications",
                "last_notification_at",
                "is_read",
            ]
        )

    def mark_all_read(self):
        """Mark all notifications in this group as read."""
        updated_count = self.notifications.filter(is_read=False).update(is_read=True, read_at=timezone.now())
        if updated_count > 0:
            self.update_counts()
        return updated_count


class NotificationSettings(BaseMessagingModel):
    """User notification preferences."""

    DIGEST_FREQUENCIES = [
        ("never", _("Never")),
        ("immediate", _("Immediate")),
        ("hourly", _("Hourly")),
        ("daily", _("Daily")),
        ("weekly", _("Weekly")),
    ]

    DELIVERY_CHANNELS = [
        ("in_app", _("In-App")),
        ("email", _("Email")),
        ("push", _("Push Notification")),
        ("sms", _("SMS")),
    ]

    user = models.OneToOneField(
        User,
        on_delete=models.CASCADE,
        related_name="notification_settings",
        verbose_name=_("user"),
    )

    # Email notifications
    email_notifications = models.BooleanField(_("email notifications"), default=True)
    email_digest_frequency = models.CharField(
        _("email digest frequency"),
        max_length=20,
        choices=DIGEST_FREQUENCIES,
        default="daily",
    )

    # Push notifications
    push_notifications = models.BooleanField(_("push notifications"), default=True)
    push_sound = models.BooleanField(_("push sound"), default=True)

    # Notification types
    notify_messages = models.BooleanField(_("notify messages"), default=True)
    notify_mentions = models.BooleanField(_("notify mentions"), default=True)
    notify_comments = models.BooleanField(_("notify comments"), default=True)
    notify_tasks = models.BooleanField(_("notify tasks"), default=True)
    notify_documents = models.BooleanField(_("notify documents"), default=True)
    notify_projects = models.BooleanField(_("notify projects"), default=True)
    notify_conflicts = models.BooleanField(_("notify conflicts"), default=True)

    # Delivery channel preferences (JSON field for flexibility)
    channel_preferences = models.JSONField(
        _("channel preferences"),
        default=dict,
        blank=True,
        help_text=_("Preferred delivery channels for each notification type"),
    )

    # Grouping preferences
    enable_grouping = models.BooleanField(_("enable grouping"), default=True)
    max_notifications_per_group = models.PositiveIntegerField(_("max notifications per group"), default=10)
    auto_collapse_groups = models.BooleanField(_("auto collapse groups"), default=True)

    # Quiet hours
    quiet_hours_enabled = models.BooleanField(_("quiet hours enabled"), default=False)
    quiet_hours_start = models.TimeField(_("quiet hours start"), null=True, blank=True)
    quiet_hours_end = models.TimeField(_("quiet hours end"), null=True, blank=True)

    class Meta:
        app_label = "messaging"
        db_table: ClassVar = "messaging_notification_settings"
        verbose_name = _("notification settings")
        verbose_name_plural = _("notification settings")

    def __str__(self) -> str:
        return f"Notification settings for {self.user.username}"

    def is_in_quiet_hours(self) -> bool:
        """Check if current time is within quiet hours."""
        if not self.quiet_hours_enabled or not self.quiet_hours_start or not self.quiet_hours_end:
            return False

        current_time = timezone.now().time()

        if self.quiet_hours_start < self.quiet_hours_end:
            # Same day range (e.g., 10:00 - 18:00)
            return self.quiet_hours_start <= current_time <= self.quiet_hours_end
        # Overnight range (e.g., 22:00 - 06:00)
        return current_time >= self.quiet_hours_start or current_time <= self.quiet_hours_end

    def should_notify(self, notification_type: str) -> bool:
        """Check if user should be notified for a specific type."""
        type_mapping = {
            "message": self.notify_messages,
            "mention": self.notify_mentions,
            "comment": self.notify_comments,
            "task": self.notify_tasks,
            "document": self.notify_documents,
            "project": self.notify_projects,
        }
        return type_mapping.get(notification_type, True)

    def get_preferred_channels(self, notification_type: str) -> list:
        """Get preferred delivery channels for a notification type."""
        if not self.channel_preferences:
            return ["in_app"]  # Default to in-app notifications

        return self.channel_preferences.get(notification_type, ["in_app"])

    def set_channel_preference(self, notification_type: str, channels: list):
        """Set preferred delivery channels for a notification type."""
        if not self.channel_preferences:
            self.channel_preferences = {}

        self.channel_preferences[notification_type] = channels
        self.save(update_fields=["channel_preferences"])

    def get_default_settings(self):
        """Get default notification settings for new users."""
        return {
            "email_notifications": True,
            "email_digest_frequency": "daily",
            "push_notifications": True,
            "push_sound": False,
            "notify_messages": True,
            "notify_mentions": True,
            "notify_comments": True,
            "notify_tasks": True,
            "notify_documents": True,
            "notify_projects": True,
            "notify_conflicts": True,
            "enable_grouping": True,
            "max_notifications_per_group": 10,
            "auto_collapse_groups": True,
            "quiet_hours_enabled": False,
            "channel_preferences": {
                "message": ["in_app", "push"],
                "mention": ["in_app", "push", "email"],
                "comment": ["in_app"],
                "task": ["in_app", "email"],
                "document": ["in_app"],
                "project": ["in_app", "email"],
                "system": ["in_app", "email"],
            },
        }


class NotificationBatch(BaseMessagingModel):
    """Batch notification delivery for efficiency."""

    BATCH_TYPES = [
        ("email_digest", _("Email Digest")),
        ("push_batch", _("Push Notification Batch")),
        ("system_batch", _("System Notification Batch")),
    ]

    BATCH_STATUS = [
        ("pending", _("Pending")),
        ("processing", _("Processing")),
        ("completed", _("Completed")),
        ("failed", _("Failed")),
    ]

    batch_type = models.CharField(_("batch type"), max_length=20, choices=BATCH_TYPES, default="email_digest")
    status = models.CharField(_("status"), max_length=20, choices=BATCH_STATUS, default="pending")
    recipient_count = models.PositiveIntegerField(_("recipient count"), default=0)
    completed_at = models.DateTimeField(_("completed at"), null=True, blank=True)
    error_message = models.TextField(_("error message"), blank=True)

    class Meta:
        app_label = "messaging"
        db_table: ClassVar = "messaging_notification_batch"
        verbose_name = _("notification batch")
        verbose_name_plural = _("notification batches")
        ordering = ["-created_at"]
        indexes = [
            models.Index(fields=["batch_type", "status"]),
            models.Index(fields=["status", "created_at"]),
        ]

    def __str__(self) -> str:
        return f"{self.get_batch_type_display()} batch ({self.status})"

    def mark_completed(self):
        """Mark batch as completed."""
        self.status = "completed"
        self.completed_at = timezone.now()
        self.save(update_fields=["status", "completed_at"])

    def mark_failed(self, error_message: str = ""):
        """Mark batch as failed."""
        self.status = "failed"
        self.error_message = error_message
        self.save(update_fields=["status", "error_message"])


class NotificationDelivery(BaseMessagingModel):
    """Track individual notification delivery attempts."""

    DELIVERY_METHODS = [
        ("email", _("Email")),
        ("push", _("Push Notification")),
        ("sms", _("SMS")),
        ("webhook", _("Webhook")),
    ]

    DELIVERY_STATUS = [
        ("pending", _("Pending")),
        ("sent", _("Sent")),
        ("delivered", _("Delivered")),
        ("failed", _("Failed")),
        ("bounced", _("Bounced")),
    ]

    notification = models.ForeignKey(
        Notification,
        on_delete=models.CASCADE,
        related_name="deliveries",
        verbose_name=_("notification"),
    )
    batch = models.ForeignKey(
        NotificationBatch,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="deliveries",
        verbose_name=_("batch"),
    )

    delivery_method = models.CharField(_("delivery method"), max_length=20, choices=DELIVERY_METHODS)
    status = models.CharField(_("status"), max_length=20, choices=DELIVERY_STATUS, default="pending")

    sent_at = models.DateTimeField(_("sent at"), null=True, blank=True)
    delivered_at = models.DateTimeField(_("delivered at"), null=True, blank=True)

    # External service tracking
    external_id = models.CharField(_("external ID"), max_length=255, blank=True)
    error_message = models.TextField(_("error message"), blank=True)
    retry_count = models.PositiveIntegerField(_("retry count"), default=0)

    class Meta:
        app_label = "messaging"
        db_table: ClassVar = "messaging_notification_delivery"
        verbose_name = _("notification delivery")
        verbose_name_plural = _("notification deliveries")
        ordering = ["-created_at"]
        indexes = [
            models.Index(fields=["notification", "delivery_method"]),
            models.Index(fields=["status", "sent_at"]),
            models.Index(fields=["external_id"]),
        ]

    def __str__(self) -> str:
        return f"{self.get_delivery_method_display()} delivery for {self.notification.id}"

    def mark_sent(self, external_id: str = ""):
        """Mark delivery as sent."""
        self.status = "sent"
        self.sent_at = timezone.now()
        if external_id:
            self.external_id = external_id
        self.save(update_fields=["status", "sent_at", "external_id"])

    def mark_delivered(self):
        """Mark delivery as delivered."""
        self.status = "delivered"
        self.delivered_at = timezone.now()
        self.save(update_fields=["status", "delivered_at"])

    def mark_failed(self, error_message: str = ""):
        """Mark delivery as failed."""
        self.status = "failed"
        self.error_message = error_message
        self.retry_count += 1
        self.save(update_fields=["status", "error_message", "retry_count"])


class InternalEmail(BaseMessagingModel):
    """Internal email addressing system for entities."""

    address = models.EmailField(_("email address"), unique=True)
    entity_type = models.CharField(_("entity type"), max_length=50)
    entity_id = models.CharField(_("entity ID"), max_length=255)
    is_active = models.BooleanField(_("active"), default=True)

    class Meta:
        app_label = "messaging"
        db_table: ClassVar = "messaging_internal_email"
        verbose_name = _("internal email")
        verbose_name_plural = _("internal emails")
        indexes = [
            models.Index(fields=["entity_type", "entity_id"]),
            models.Index(fields=["is_active"]),
        ]

    def __str__(self) -> str:
        return f"{self.address} -> {self.entity_type}:{self.entity_id}"


class InternalEmailMessage(BaseMessagingModel):
    """Messages sent to internal email addresses."""

    PROCESSING_STATUS = [
        ("pending", _("Pending")),
        ("processed", _("Processed")),
        ("failed", _("Failed")),
        ("ignored", _("Ignored")),
    ]

    internal_email = models.ForeignKey(
        InternalEmail,
        on_delete=models.CASCADE,
        related_name="messages",
        verbose_name=_("internal email"),
    )

    from_address = models.EmailField(_("from address"))
    from_name = models.CharField(_("from name"), max_length=255, blank=True)
    subject = models.CharField(_("subject"), max_length=255)
    body_text = models.TextField(_("body text"), blank=True)
    body_html = models.TextField(_("body HTML"), blank=True)

    received_at = models.DateTimeField(_("received at"), default=timezone.now)
    processed_at = models.DateTimeField(_("processed at"), null=True, blank=True)
    processing_status = models.CharField(
        _("processing status"),
        max_length=20,
        choices=PROCESSING_STATUS,
        default="pending",
    )
    processing_notes = models.TextField(_("processing notes"), blank=True)

    # Created content references
    created_chat_message = models.ForeignKey(
        ChatMessage,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="source_emails",
        verbose_name=_("created chat message"),
    )
    created_comment = models.ForeignKey(
        Comment,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="source_emails",
        verbose_name=_("created comment"),
    )

    class Meta:
        app_label = "messaging"
        db_table: ClassVar = "messaging_internal_email_message"
        verbose_name = _("internal email message")
        verbose_name_plural = _("internal email messages")
        ordering = ["-received_at"]
        indexes = [
            models.Index(fields=["internal_email", "received_at"]),
            models.Index(fields=["processing_status", "received_at"]),
            models.Index(fields=["from_address", "received_at"]),
        ]

    def __str__(self) -> str:
        return f"Email from {self.from_address}: {self.subject}"

    def mark_processed(self, notes: str = ""):
        """Mark email as processed."""
        self.processing_status = "processed"
        self.processed_at = timezone.now()
        if notes:
            self.processing_notes = notes
        self.save(update_fields=["processing_status", "processed_at", "processing_notes"])

    def mark_failed(self, error_message: str = ""):
        """Mark email processing as failed."""
        self.processing_status = "failed"
        self.processing_notes = error_message
        self.save(update_fields=["processing_status", "processing_notes"])


class CommentFlag(BaseMessagingModel):
    """Flag model for comment moderation system."""

    FLAG_REASONS = [
        ("spam", _("Spam")),
        ("inappropriate", _("Inappropriate Content")),
        ("harassment", _("Harassment")),
        ("hate_speech", _("Hate Speech")),
        ("misinformation", _("Misinformation")),
        ("off_topic", _("Off Topic")),
        ("duplicate", _("Duplicate")),
        ("other", _("Other")),
    ]

    comment = models.ForeignKey(
        Comment,
        on_delete=models.CASCADE,
        related_name="flags",
        verbose_name=_("comment"),
    )
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name="messaging_comment_flags",
        verbose_name=_("flagging user"),
    )
    reason = models.CharField(
        _("flag reason"),
        max_length=20,
        choices=FLAG_REASONS,
        default="inappropriate",
    )
    description = models.TextField(_("description"), blank=True)

    # Moderation tracking
    is_resolved = models.BooleanField(_("is resolved"), default=False)
    resolved_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="resolved_flags",
        verbose_name=_("resolved by"),
    )
    resolved_at = models.DateTimeField(_("resolved at"), null=True, blank=True)
    moderator_notes = models.TextField(_("moderator notes"), blank=True)

    class Meta:
        app_label = "messaging"
        db_table: ClassVar = "messaging_comment_flag"
        verbose_name = _("comment flag")
        verbose_name_plural = _("comment flags")
        unique_together = ["comment", "user"]  # Prevent duplicate flags from same user
        ordering = ["-created_at"]
        indexes = [
            models.Index(fields=["comment", "is_resolved"]),
            models.Index(fields=["user", "created_at"]),
            models.Index(fields=["reason", "is_resolved"]),
            models.Index(fields=["is_resolved", "created_at"]),
        ]

    def __str__(self) -> str:
        return f"Flag by {self.user.username} on comment {self.comment.id}: {self.get_reason_display()}"

    def resolve(self, moderator: User, notes: str = ""):
        """Mark flag as resolved by a moderator."""
        self.is_resolved = True
        self.resolved_by = moderator
        self.resolved_at = timezone.now()
        self.moderator_notes = notes
        self.save(
            update_fields=[
                "is_resolved",
                "resolved_by",
                "resolved_at",
                "moderator_notes",
            ]
        )


class ModerationAction(BaseMessagingModel):
    """Track moderation actions taken on comments."""

    ACTION_TYPES = [
        ("approve", _("Approve")),
        ("hide", _("Hide")),
        ("delete", _("Delete")),
        ("warn", _("Warn User")),
        ("suspend", _("Suspend User")),
        ("no_action", _("No Action Required")),
    ]

    comment = models.ForeignKey(
        Comment,
        on_delete=models.CASCADE,
        related_name="moderation_actions",
        verbose_name=_("comment"),
    )
    moderator = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name="moderation_actions",
        verbose_name=_("moderator"),
    )
    action_type = models.CharField(
        _("action type"),
        max_length=20,
        choices=ACTION_TYPES,
        default="no_action",
    )
    reason = models.TextField(_("reason"))
    notes = models.TextField(_("internal notes"), blank=True)

    # Track what flags this action addressed
    resolved_flags = models.ManyToManyField(
        CommentFlag,
        related_name="moderation_actions",
        blank=True,
        verbose_name=_("resolved flags"),
    )

    class Meta:
        app_label = "messaging"
        db_table: ClassVar = "messaging_moderation_action"
        verbose_name = _("moderation action")
        verbose_name_plural = _("moderation actions")
        ordering = ["-created_at"]
        indexes = [
            models.Index(fields=["comment", "created_at"]),
            models.Index(fields=["moderator", "created_at"]),
            models.Index(fields=["action_type", "created_at"]),
        ]

    def __str__(self) -> str:
        return f"{self.get_action_type_display()} on comment {self.comment.id} by {self.moderator.username}"


class CollaborationSettings(BaseMessagingModel):
    """User collaboration settings for messaging and teamwork features."""

    user = models.OneToOneField(
        User,
        on_delete=models.CASCADE,
        related_name="collaboration_settings",
        verbose_name=_("user"),
    )

    # Thread and conversation settings
    auto_collapse_threads = models.BooleanField(
        default=True,
        verbose_name=_("auto collapse threads"),
        help_text=_("Automatically collapse long message threads"),
    )

    show_thread_previews = models.BooleanField(
        default=True,
        verbose_name=_("show thread previews"),
        help_text=_("Show previews of threaded conversations"),
    )

    # Notification preferences
    mention_notifications = models.BooleanField(
        default=True,
        verbose_name=_("mention notifications"),
        help_text=_("Receive notifications when mentioned"),
    )

    mention_sound = models.BooleanField(
        default=False,
        verbose_name=_("mention sound"),
        help_text=_("Play sound when mentioned"),
    )

    mention_email = models.BooleanField(
        default=False,
        verbose_name=_("mention email"),
        help_text=_("Send email notifications for mentions"),
    )

    # Reaction settings
    show_reaction_tooltips = models.BooleanField(
        default=True,
        verbose_name=_("show reaction tooltips"),
        help_text=_("Show tooltips for message reactions"),
    )

    reaction_notifications = models.BooleanField(
        default=False,
        verbose_name=_("reaction notifications"),
        help_text=_("Receive notifications for message reactions"),
    )

    # Customization options
    custom_emoji_set = models.CharField(
        max_length=50,
        default="default",
        choices=[
            ("default", _("Default")),
            ("minimal", _("Minimal")),
            ("extended", _("Extended")),
            ("custom", _("Custom")),
        ],
        verbose_name=_("custom emoji set"),
        help_text=_("Choose emoji set for reactions"),
    )

    # Display preferences
    compact_message_view = models.BooleanField(
        default=False,
        verbose_name=_("compact message view"),
        help_text=_("Use compact layout for messages"),
    )

    show_message_timestamps = models.BooleanField(
        default=True,
        verbose_name=_("show message timestamps"),
        help_text=_("Display timestamps on messages"),
    )

    show_user_status = models.BooleanField(
        default=True,
        verbose_name=_("show user status"),
        help_text=_("Display online/offline status of users"),
    )

    # Privacy settings
    allow_direct_messages = models.BooleanField(
        default=True,
        verbose_name=_("allow direct messages"),
        help_text=_("Allow other users to send direct messages"),
    )

    show_read_receipts = models.BooleanField(
        default=True,
        verbose_name=_("show read receipts"),
        help_text=_("Show when you have read messages"),
    )

    # Workspace preferences
    default_workspace_view = models.CharField(
        max_length=20,
        default="conversations",
        choices=[
            ("conversations", _("Conversations")),
            ("channels", _("Channels")),
            ("mentions", _("Mentions")),
            ("threads", _("Threads")),
        ],
        verbose_name=_("default workspace view"),
        help_text=_("Default view when opening messaging workspace"),
    )

    class Meta:
        verbose_name = _("collaboration settings")
        verbose_name_plural = _("collaboration settings")

    def __str__(self):
        return f"Collaboration settings for {self.user.get_full_name() or self.user.username}"

    @classmethod
    def get_or_create_for_user(cls, user):
        """Get or create collaboration settings for a user."""
        settings, created = cls.objects.get_or_create(user=user)
        return settings
